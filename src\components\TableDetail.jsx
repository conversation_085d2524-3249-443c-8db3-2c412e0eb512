import { useState } from 'react'

function TableDetail({ table, schemaData, searchQuery }) {
  const [activeTab, setActiveTab] = useState('columns')

  const highlightText = (text, query) => {
    if (!query) return text
    
    const regex = new RegExp(`(${query})`, 'gi')
    const parts = text.split(regex)
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <span key={index} className="search-highlight">
          {part}
        </span>
      ) : part
    )
  }

  const getRelatedTables = () => {
    const related = new Set()
    
    // Tables this table references (via foreign keys)
    table.foreignKeys.forEach(fk => {
      related.add(fk.referencedTable)
    })
    
    // Tables that reference this table
    schemaData.tables.forEach(otherTable => {
      otherTable.foreignKeys.forEach(fk => {
        if (fk.referencedTable === table.name) {
          related.add(otherTable.name)
        }
      })
    })
    
    return Array.from(related)
  }

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      // Could add a toast notification here
      console.log('Copied to clipboard')
    })
  }

  const exportTableMarkdown = () => {
    let markdown = `# ${table.name}\n\n`
    
    if (table.description) {
      markdown += `${table.description}\n\n`
    }
    
    markdown += `## Columns\n\n`
    markdown += `| Column | Type | Nullable | Primary Key | Foreign Key | Unique | Default |\n`
    markdown += `|--------|------|----------|-------------|-------------|--------|---------|\n`
    
    table.columns.forEach(column => {
      const fkRef = column.foreignKey ? `${column.foreignKey.table}.${column.foreignKey.column}` : ''
      markdown += `| ${column.name} | ${column.type} | ${column.nullable ? 'Yes' : 'No'} | ${column.isPrimaryKey ? 'Yes' : 'No'} | ${fkRef} | ${column.isUnique ? 'Yes' : 'No'} | ${column.defaultValue || ''} |\n`
    })
    
    if (table.foreignKeys.length > 0) {
      markdown += `\n## Foreign Keys\n\n`
      table.foreignKeys.forEach(fk => {
        markdown += `- **${fk.column}** → ${fk.referencedTable}.${fk.referencedColumn}\n`
      })
    }
    
    copyToClipboard(markdown)
  }

  const relatedTables = getRelatedTables()

  return (
    <div className="flex-1 flex flex-col bg-white dark:bg-gray-800">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {highlightText(table.name, searchQuery)}
            </h1>
            {table.description && (
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                {table.description}
              </p>
            )}
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={exportTableMarkdown}
              className="px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              📋 Copy Markdown
            </button>
            <button className="px-3 py-2 text-sm bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors">
              📖 Explain Table
            </button>
          </div>
        </div>
        
        {/* Stats */}
        <div className="flex space-x-6 mt-4 text-sm text-gray-600 dark:text-gray-400">
          <span>{table.columns.length} columns</span>
          <span>{table.primaryKeys.length} primary key{table.primaryKeys.length !== 1 ? 's' : ''}</span>
          <span>{table.foreignKeys.length} foreign key{table.foreignKeys.length !== 1 ? 's' : ''}</span>
          {relatedTables.length > 0 && (
            <span>{relatedTables.length} related table{relatedTables.length !== 1 ? 's' : ''}</span>
          )}
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8 px-6">
          {['columns', 'relationships', 'constraints'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`py-4 px-1 border-b-2 font-medium text-sm capitalize transition-colors ${
                activeTab === tab
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              {tab}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-auto custom-scrollbar">
        {activeTab === 'columns' && (
          <div className="p-6">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Column</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Type</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Constraints</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Default</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Description</th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {table.columns.map((column) => (
                    <tr key={column.name} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className="text-sm font-medium text-gray-900 dark:text-white">
                            {highlightText(column.name, searchQuery)}
                          </span>
                          {column.isPrimaryKey && (
                            <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                              PK
                            </span>
                          )}
                          {column.foreignKey && (
                            <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                              FK
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {column.type}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-wrap gap-1">
                          {!column.nullable && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                              NOT NULL
                            </span>
                          )}
                          {column.isUnique && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                              UNIQUE
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {column.defaultValue || '-'}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                        {column.description || '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeTab === 'relationships' && (
          <div className="p-6">
            <div className="space-y-6">
              {/* Foreign Keys */}
              {table.foreignKeys.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    References (Foreign Keys)
                  </h3>
                  <div className="space-y-3">
                    {table.foreignKeys.map((fk, index) => (
                      <div key={index} className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                        <div className="flex items-center">
                          <span className="text-sm font-medium text-gray-900 dark:text-white">
                            {fk.column}
                          </span>
                          <span className="mx-2 text-gray-500">→</span>
                          <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                            {fk.referencedTable}.{fk.referencedColumn}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Referenced By */}
              {relatedTables.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    Referenced By
                  </h3>
                  <div className="space-y-3">
                    {schemaData.tables
                      .filter(t => t.foreignKeys.some(fk => fk.referencedTable === table.name))
                      .map((referencingTable) => (
                        <div key={referencingTable.name} className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                          <div className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                            {referencingTable.name}
                          </div>
                          {referencingTable.foreignKeys
                            .filter(fk => fk.referencedTable === table.name)
                            .map((fk, index) => (
                              <div key={index} className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                <span>{fk.column}</span>
                                <span className="mx-2">→</span>
                                <span>{fk.referencedColumn}</span>
                              </div>
                            ))}
                        </div>
                      ))}
                  </div>
                </div>
              )}

              {table.foreignKeys.length === 0 && relatedTables.length === 0 && (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  No relationships found for this table
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'constraints' && (
          <div className="p-6">
            <div className="space-y-4">
              {/* Primary Keys */}
              {table.primaryKeys.length > 0 && (
                <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Primary Key</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {table.primaryKeys.join(', ')}
                  </p>
                </div>
              )}

              {/* Unique Constraints */}
              {table.columns.filter(col => col.isUnique).length > 0 && (
                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Unique Constraints</h4>
                  <div className="space-y-1">
                    {table.columns
                      .filter(col => col.isUnique)
                      .map(col => (
                        <p key={col.name} className="text-sm text-gray-600 dark:text-gray-400">
                          {col.name}
                        </p>
                      ))}
                  </div>
                </div>
              )}

              {/* Not Null Constraints */}
              {table.columns.filter(col => !col.nullable).length > 0 && (
                <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Not Null Constraints</h4>
                  <div className="space-y-1">
                    {table.columns
                      .filter(col => !col.nullable)
                      .map(col => (
                        <p key={col.name} className="text-sm text-gray-600 dark:text-gray-400">
                          {col.name}
                        </p>
                      ))}
                  </div>
                </div>
              )}

              {table.primaryKeys.length === 0 && 
               table.columns.filter(col => col.isUnique).length === 0 && 
               table.columns.filter(col => !col.nullable).length === 0 && (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  No constraints found for this table
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default TableDetail
