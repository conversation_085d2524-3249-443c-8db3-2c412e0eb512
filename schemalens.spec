# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# Get the current directory
current_dir = os.path.dirname(os.path.abspath(SPEC))

# Define data files to include
datas = [
    (os.path.join(current_dir, 'index.html'), '.'),
    (os.path.join(current_dir, 'manifest.json'), '.'),
    (os.path.join(current_dir, 'sw.js'), '.'),
    (os.path.join(current_dir, 'sample-schema.xml'), '.'),
    (os.path.join(current_dir, 'src'), 'src'),
]

# Filter out files that don't exist
datas = [(src, dst) for src, dst in datas if os.path.exists(src)]

block_cipher = None

a = Analysis(
    ['schemalens_server.py'],
    pathex=[current_dir],
    binaries=[],
    datas=datas,
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'http.server',
        'socketserver',
        'webbrowser',
        'threading',
        'tempfile',
        'shutil',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
        'jupyter',
        'IPython',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SchemaLens',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Set to False for GUI mode, True for console mode
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='schemalens.ico' if os.path.exists('schemalens.ico') else None,
    version='version_info.txt' if os.path.exists('version_info.txt') else None,
)
