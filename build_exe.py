#!/usr/bin/env python3
"""
Build SchemaLens Windows Executable
Creates a standalone .exe file using PyInstaller
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_requirements():
    """Check if required tools are available"""
    print("🔍 Checking requirements...")
    
    # Check Python version
    if sys.version_info < (3, 6):
        print("❌ Python 3.6+ required")
        return False
    
    print(f"✅ Python {sys.version.split()[0]}")
    
    # Check PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller not found")
        print("📦 Installing PyInstaller...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
            print("✅ PyInstaller installed")
        except subprocess.CalledProcessError:
            print("❌ Failed to install PyInstaller")
            return False
    
    return True

def create_icon():
    """Create application icon"""
    print("🎨 Creating application icon...")
    
    try:
        subprocess.run([sys.executable, 'create_icon.py'], check=True)
        if os.path.exists('schemalens.ico'):
            print("✅ Icon created successfully")
            return True
    except subprocess.CalledProcessError:
        pass
    
    print("⚠️ Using default icon")
    return False

def build_executable():
    """Build the executable using PyInstaller"""
    print("🔨 Building executable...")
    
    # Clean previous builds
    for dir_name in ['build', 'dist']:
        if os.path.exists(dir_name):
            print(f"🧹 Cleaning {dir_name}/")
            shutil.rmtree(dir_name)
    
    # Build command
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',
        '--noconfirm',
        'schemalens.spec'
    ]
    
    try:
        print("⚙️ Running PyInstaller...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Build completed successfully!")
            return True
        else:
            print("❌ Build failed!")
            print("Error output:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Build error: {e}")
        return False

def create_installer_script():
    """Create a simple installer script"""
    installer_script = '''@echo off
title SchemaLens Installer
echo.
echo ========================================
echo   SchemaLens Database Schema Explorer
echo ========================================
echo.
echo This will install SchemaLens to your system.
echo.
pause

set "INSTALL_DIR=%USERPROFILE%\\SchemaLens"
echo Creating installation directory: %INSTALL_DIR%
mkdir "%INSTALL_DIR%" 2>nul

echo Copying files...
copy "SchemaLens.exe" "%INSTALL_DIR%\\" >nul
if errorlevel 1 (
    echo Error: Failed to copy executable
    pause
    exit /b 1
)

echo Creating desktop shortcut...
set "SHORTCUT=%USERPROFILE%\\Desktop\\SchemaLens.lnk"
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\SchemaLens.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Database Schema Explorer'; $Shortcut.Save()"

echo Creating start menu entry...
set "STARTMENU=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs"
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTMENU%\\SchemaLens.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\SchemaLens.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Database Schema Explorer'; $Shortcut.Save()"

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo SchemaLens has been installed to: %INSTALL_DIR%
echo Desktop shortcut created: %USERPROFILE%\\Desktop\\SchemaLens.lnk
echo Start menu entry created
echo.
echo You can now run SchemaLens from:
echo - Desktop shortcut
echo - Start menu
echo - Command: "%INSTALL_DIR%\\SchemaLens.exe"
echo.
pause
'''
    
    with open('dist/install.bat', 'w', encoding='utf-8') as f:
        f.write(installer_script)
    
    print("✅ Installer script created: dist/install.bat")

def create_readme():
    """Create README for the executable"""
    readme_content = '''# SchemaLens Executable

## Quick Start

1. **Run directly**: Double-click `SchemaLens.exe`
2. **Install**: Run `install.bat` to install to your system

## What's Included

- `SchemaLens.exe` - Main application (standalone, no installation required)
- `install.bat` - Optional installer script
- `README.txt` - This file

## Features

- 📊 Interactive database schema explorer
- 🔍 Advanced search and filtering
- 📈 Comprehensive statistics and audit analysis
- 📋 ER diagram generation
- 🌙 Dark mode support
- 📱 Responsive design

## System Requirements

- Windows 7/8/10/11 (64-bit recommended)
- No additional software required
- Modern web browser (automatically opens)

## Usage

1. Run SchemaLens.exe
2. Click "Start SchemaLens" in the GUI
3. Upload your XML schema file
4. Explore your database structure!

## Troubleshooting

**Antivirus Warning**: Some antivirus software may flag the executable as suspicious. This is normal for PyInstaller-built applications. Add an exception if needed.

**Port Issues**: If port 8080 is busy, SchemaLens will automatically find another available port.

**Browser Issues**: If the browser doesn't open automatically, manually navigate to the URL shown in the application window.

## Support

For issues or questions, check the original SchemaLens documentation.

---
SchemaLens v1.0.0 - Database Schema Explorer
'''
    
    with open('dist/README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ README created: dist/README.txt")

def package_release():
    """Package the final release"""
    print("📦 Packaging release...")
    
    if not os.path.exists('dist/SchemaLens.exe'):
        print("❌ Executable not found!")
        return False
    
    # Create installer and documentation
    create_installer_script()
    create_readme()
    
    # Get file size
    exe_size = os.path.getsize('dist/SchemaLens.exe')
    size_mb = round(exe_size / (1024 * 1024), 1)
    
    print(f"✅ Release packaged successfully!")
    print(f"📁 Location: dist/")
    print(f"📊 Executable size: {size_mb}MB")
    print(f"📄 Files included:")
    print(f"   - SchemaLens.exe (main application)")
    print(f"   - install.bat (optional installer)")
    print(f"   - README.txt (documentation)")
    
    return True

def main():
    """Main build process"""
    print("🚀 Building SchemaLens Windows Executable")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        print("❌ Requirements check failed")
        return False
    
    # Create icon
    create_icon()
    
    # Build executable
    if not build_executable():
        print("❌ Build failed")
        return False
    
    # Package release
    if not package_release():
        print("❌ Packaging failed")
        return False
    
    print("\n🎉 Build completed successfully!")
    print("\n📋 Next steps:")
    print("1. Test the executable: dist/SchemaLens.exe")
    print("2. Share the dist/ folder or just the .exe file")
    print("3. Users can run install.bat for system installation")
    print("\n✨ SchemaLens is ready for distribution!")
    
    return True

if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n❌ Build failed. Check the errors above.")
        input("Press Enter to exit...")
        sys.exit(1)
    else:
        input("\nPress Enter to exit...")
        sys.exit(0)
