import { useState, useRef, useEffect } from 'react'

function SearchBar({ searchQuery, onSearchChange, schemaData }) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [suggestions, setSuggestions] = useState([])
  const [selectedSuggestion, setSelectedSuggestion] = useState(-1)
  const inputRef = useRef(null)

  useEffect(() => {
    if (searchQuery && schemaData?.tables) {
      const newSuggestions = []
      
      schemaData.tables.forEach(table => {
        // Add table suggestions
        if (table.name.toLowerCase().includes(searchQuery.toLowerCase())) {
          newSuggestions.push({
            type: 'table',
            name: table.name,
            description: table.description || 'Table',
            table: table.name
          })
        }
        
        // Add column suggestions
        table.columns.forEach(column => {
          if (column.name.toLowerCase().includes(searchQuery.toLowerCase())) {
            newSuggestions.push({
              type: 'column',
              name: column.name,
              description: `${column.type} in ${table.name}`,
              table: table.name,
              column: column.name
            })
          }
        })
      })
      
      setSuggestions(newSuggestions.slice(0, 8)) // Limit to 8 suggestions
    } else {
      setSuggestions([])
    }
    setSelectedSuggestion(-1)
  }, [searchQuery, schemaData])

  const handleKeyDown = (e) => {
    if (suggestions.length === 0) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedSuggestion(prev => 
          prev < suggestions.length - 1 ? prev + 1 : prev
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedSuggestion(prev => prev > 0 ? prev - 1 : -1)
        break
      case 'Enter':
        e.preventDefault()
        if (selectedSuggestion >= 0) {
          handleSuggestionClick(suggestions[selectedSuggestion])
        }
        break
      case 'Escape':
        setIsExpanded(false)
        inputRef.current?.blur()
        break
    }
  }

  const handleSuggestionClick = (suggestion) => {
    onSearchChange(suggestion.name)
    setIsExpanded(false)
    inputRef.current?.blur()
  }

  const clearSearch = () => {
    onSearchChange('')
    setIsExpanded(false)
    inputRef.current?.focus()
  }

  return (
    <div className="relative">
      <div className={`flex items-center transition-all duration-200 ${
        isExpanded ? 'w-80' : 'w-64'
      }`}>
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            ref={inputRef}
            type="text"
            placeholder="Search tables and columns..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            onFocus={() => setIsExpanded(true)}
            onBlur={() => {
              // Delay hiding to allow clicking on suggestions
              setTimeout(() => setIsExpanded(false), 200)
            }}
            onKeyDown={handleKeyDown}
            className="block w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
          />
          {searchQuery && (
            <button
              onClick={clearSearch}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              <svg className="h-4 w-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Suggestions Dropdown */}
      {isExpanded && suggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md shadow-lg z-50 max-h-64 overflow-y-auto">
          {suggestions.map((suggestion, index) => (
            <button
              key={`${suggestion.type}-${suggestion.table}-${suggestion.name}`}
              onClick={() => handleSuggestionClick(suggestion)}
              className={`w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                index === selectedSuggestion ? 'bg-blue-50 dark:bg-blue-900/20' : ''
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center">
                    <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium mr-2 ${
                      suggestion.type === 'table' 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                    }`}>
                      {suggestion.type === 'table' ? '🗂️' : '📄'} {suggestion.type}
                    </span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {suggestion.name}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {suggestion.description}
                  </p>
                </div>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  )
}

export default SearchBar
