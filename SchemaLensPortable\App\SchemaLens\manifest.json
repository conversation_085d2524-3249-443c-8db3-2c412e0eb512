{"name": "SchemaLens - Database Schema Explorer", "short_name": "Schema<PERSON>ens", "description": "Interactive database schema explorer and analyzer with advanced filtering and statistics", "version": "1.0.0", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#3b82f6", "orientation": "any", "scope": "/", "lang": "en", "categories": ["productivity", "developer", "utilities"], "screenshots": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='1280' height='720' viewBox='0 0 1280 720'><rect width='1280' height='720' fill='%23f8fafc'/><text x='640' y='360' text-anchor='middle' font-family='Arial' font-size='48' fill='%233b82f6'>📊 SchemaLens</text><text x='640' y='420' text-anchor='middle' font-family='Arial' font-size='24' fill='%236b7280'>Database Schema Explorer</text></svg>", "sizes": "1280x720", "type": "image/svg+xml", "form_factor": "wide", "label": "SchemaLens main interface"}, {"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='750' height='1334' viewBox='0 0 750 1334'><rect width='750' height='1334' fill='%23f8fafc'/><text x='375' y='667' text-anchor='middle' font-family='Arial' font-size='36' fill='%233b82f6'>📊 SchemaLens</text><text x='375' y='720' text-anchor='middle' font-family='Arial' font-size='18' fill='%236b7280'>Mobile Ready</text></svg>", "sizes": "750x1334", "type": "image/svg+xml", "form_factor": "narrow", "label": "SchemaLens mobile interface"}], "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='192' height='192' viewBox='0 0 192 192'><rect width='192' height='192' fill='%233b82f6' rx='24'/><text x='96' y='130' text-anchor='middle' font-size='120'>📊</text></svg>", "sizes": "192x192", "type": "image/svg+xml", "purpose": "any"}, {"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='512' height='512' viewBox='0 0 512 512'><rect width='512' height='512' fill='%233b82f6' rx='64'/><text x='256' y='340' text-anchor='middle' font-size='320'>📊</text></svg>", "sizes": "512x512", "type": "image/svg+xml", "purpose": "any"}, {"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='192' height='192' viewBox='0 0 192 192'><rect width='192' height='192' fill='%23ffffff'/><rect width='144' height='144' x='24' y='24' fill='%233b82f6' rx='16'/><text x='96' y='120' text-anchor='middle' font-size='80' fill='white'>📊</text></svg>", "sizes": "192x192", "type": "image/svg+xml", "purpose": "maskable"}], "shortcuts": [{"name": "Upload Sc<PERSON>a", "short_name": "Upload", "description": "Upload a new XML schema file", "url": "/?action=upload", "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='96' height='96' viewBox='0 0 96 96'><rect width='96' height='96' fill='%2310b981' rx='12'/><text x='48' y='65' text-anchor='middle' font-size='60'>📁</text></svg>", "sizes": "96x96", "type": "image/svg+xml"}]}, {"name": "View Statistics", "short_name": "Stats", "description": "View schema statistics and analysis", "url": "/?view=statistics", "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='96' height='96' viewBox='0 0 96 96'><rect width='96' height='96' fill='%23f59e0b' rx='12'/><text x='48' y='65' text-anchor='middle' font-size='60'>📈</text></svg>", "sizes": "96x96", "type": "image/svg+xml"}]}, {"name": "Generate Diagram", "short_name": "Diagram", "description": "Generate ER diagram", "url": "/?view=diagram", "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='96' height='96' viewBox='0 0 96 96'><rect width='96' height='96' fill='%236366f1' rx='12'/><text x='48' y='65' text-anchor='middle' font-size='60'>🔗</text></svg>", "sizes": "96x96", "type": "image/svg+xml"}]}], "file_handlers": [{"action": "/", "accept": {"application/xml": [".xml"], "text/xml": [".xml"]}}], "share_target": {"action": "/", "method": "POST", "enctype": "multipart/form-data", "params": {"files": [{"name": "schema", "accept": ["application/xml", "text/xml", ".xml"]}]}}, "protocol_handlers": [{"protocol": "web+schemalens", "url": "/?schema=%s"}]}