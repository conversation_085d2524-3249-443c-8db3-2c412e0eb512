#!/usr/bin/env python3
"""
Build All SchemaLens Executables
Creates multiple executable formats for different platforms and use cases
"""

import os
import sys
import subprocess
import shutil
import json
from pathlib import Path

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f"  {title}")
    print("=" * 60)

def run_command(cmd, description):
    """Run a command and return success status"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed")
            return True
        else:
            print(f"❌ {description} failed")
            print(f"Error: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {description} failed: {e}")
        return False

def build_python_exe():
    """Build Python-based executable"""
    print_header("Building Python Executable (PyInstaller)")
    
    if not os.path.exists('schemalens_server.py'):
        print("❌ schemalens_server.py not found")
        return False
    
    # Create icon if needed
    if not os.path.exists('schemalens.ico'):
        run_command('python create_icon.py', 'Creating icon')
    
    # Build with PyInstaller
    success = run_command('python build_exe.py', 'Building Python executable')
    
    if success and os.path.exists('dist/SchemaLens.exe'):
        size_mb = round(os.path.getsize('dist/SchemaLens.exe') / (1024 * 1024), 1)
        print(f"📊 Python executable size: {size_mb}MB")
        print(f"📁 Location: dist/SchemaLens.exe")
        return True
    
    return False

def build_electron_app():
    """Build Electron-based application"""
    print_header("Building Electron Application")
    
    # Check if Node.js is available
    if not run_command('node --version', 'Checking Node.js'):
        print("❌ Node.js not found. Please install Node.js to build Electron app")
        return False
    
    # Check if npm is available
    if not run_command('npm --version', 'Checking npm'):
        print("❌ npm not found")
        return False
    
    # Install dependencies
    if not run_command('npm install electron electron-builder --save-dev', 'Installing Electron dependencies'):
        return False
    
    # Build for Windows
    success = run_command('npx electron-builder --win --x64', 'Building Electron app for Windows')
    
    if success:
        print("✅ Electron build completed")
        # Check output directory
        if os.path.exists('electron-dist'):
            files = os.listdir('electron-dist')
            print(f"📁 Electron output: {files}")
        return True
    
    return False

def build_standalone_html():
    """Build standalone HTML version"""
    print_header("Building Standalone HTML")
    
    success = run_command('python build_standalone.py', 'Building standalone HTML')
    
    if success and os.path.exists('SchemaLens-Complete.html'):
        size_kb = round(os.path.getsize('SchemaLens-Complete.html') / 1024, 1)
        print(f"📊 Standalone HTML size: {size_kb}KB")
        print(f"📁 Location: SchemaLens-Complete.html")
        return True
    
    return False

def create_portable_package():
    """Create portable package with multiple options"""
    print_header("Creating Portable Package")
    
    portable_dir = "SchemaLens-Portable"
    
    # Clean and create directory
    if os.path.exists(portable_dir):
        shutil.rmtree(portable_dir)
    os.makedirs(portable_dir)
    
    # Copy different executable options
    executables_found = []
    
    # Python executable
    if os.path.exists('dist/SchemaLens.exe'):
        shutil.copy2('dist/SchemaLens.exe', f'{portable_dir}/SchemaLens-Python.exe')
        executables_found.append('Python-based executable (PyInstaller)')
    
    # Electron executable
    electron_exe = None
    if os.path.exists('electron-dist'):
        for root, dirs, files in os.walk('electron-dist'):
            for file in files:
                if file.endswith('.exe') and 'SchemaLens' in file:
                    electron_exe = os.path.join(root, file)
                    break
            if electron_exe:
                break
    
    if electron_exe:
        shutil.copy2(electron_exe, f'{portable_dir}/SchemaLens-Electron.exe')
        executables_found.append('Electron-based executable')
    
    # Standalone HTML
    if os.path.exists('SchemaLens-Complete.html'):
        shutil.copy2('SchemaLens-Complete.html', f'{portable_dir}/SchemaLens-Standalone.html')
        executables_found.append('Standalone HTML file')
    
    # Create README
    readme_content = f"""# SchemaLens Portable Package

## Available Executables

{chr(10).join(f"• {exe}" for exe in executables_found)}

## Quick Start

### Option 1: Python Executable (Recommended)
- Run: `SchemaLens-Python.exe`
- Size: ~{round(os.path.getsize('dist/SchemaLens.exe') / (1024 * 1024), 1) if os.path.exists('dist/SchemaLens.exe') else 'N/A'}MB
- Features: Full GUI, auto-opens browser
- Requirements: None (standalone)

### Option 2: Electron Application
- Run: `SchemaLens-Electron.exe`
- Features: Native desktop app, no browser needed
- Requirements: None (standalone)

### Option 3: Standalone HTML
- Open: `SchemaLens-Standalone.html` in any browser
- Size: ~{round(os.path.getsize('SchemaLens-Complete.html') / 1024, 1) if os.path.exists('SchemaLens-Complete.html') else 'N/A'}KB
- Features: Works completely offline
- Requirements: Modern web browser

## Features

- 📊 Interactive database schema explorer
- 🔍 Advanced search and filtering
- 📈 Comprehensive statistics and audit analysis
- 📋 ER diagram generation
- 🌙 Dark mode support
- 📱 Responsive design

## System Requirements

- Windows 7/8/10/11 (64-bit recommended)
- Modern web browser (for HTML version)
- No additional software required

## Troubleshooting

**Antivirus Warning**: Some antivirus software may flag executables as suspicious. This is normal for packaged applications. Add an exception if needed.

**Port Issues**: If port 8080 is busy, the application will automatically find another available port.

---
SchemaLens v1.0.0 - Database Schema Explorer
"""
    
    with open(f'{portable_dir}/README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    # Create launcher script
    launcher_script = """@echo off
title SchemaLens Launcher
echo.
echo ========================================
echo   SchemaLens Database Schema Explorer
echo ========================================
echo.
echo Available options:
echo.
echo 1. Python Executable (Recommended)
echo 2. Electron Application
echo 3. Standalone HTML
echo 4. Exit
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" (
    if exist "SchemaLens-Python.exe" (
        echo Starting Python executable...
        start "" "SchemaLens-Python.exe"
    ) else (
        echo Python executable not found!
        pause
    )
) else if "%choice%"=="2" (
    if exist "SchemaLens-Electron.exe" (
        echo Starting Electron application...
        start "" "SchemaLens-Electron.exe"
    ) else (
        echo Electron executable not found!
        pause
    )
) else if "%choice%"=="3" (
    if exist "SchemaLens-Standalone.html" (
        echo Opening standalone HTML...
        start "" "SchemaLens-Standalone.html"
    ) else (
        echo Standalone HTML not found!
        pause
    )
) else if "%choice%"=="4" (
    exit
) else (
    echo Invalid choice!
    pause
    goto start
)
"""
    
    with open(f'{portable_dir}/Launch-SchemaLens.bat', 'w', encoding='utf-8') as f:
        f.write(launcher_script)
    
    print(f"✅ Portable package created: {portable_dir}/")
    print(f"📦 Contains {len(executables_found)} executable options")
    
    return True

def create_summary():
    """Create build summary"""
    print_header("Build Summary")
    
    summary = {
        "build_date": "2024-01-01",  # Would be actual date
        "version": "1.0.0",
        "executables": []
    }
    
    # Check what was built
    if os.path.exists('dist/SchemaLens.exe'):
        size_mb = round(os.path.getsize('dist/SchemaLens.exe') / (1024 * 1024), 1)
        summary["executables"].append({
            "type": "Python Executable",
            "file": "dist/SchemaLens.exe",
            "size": f"{size_mb}MB",
            "platform": "Windows",
            "features": ["GUI", "Auto-browser", "Standalone"]
        })
    
    if os.path.exists('SchemaLens-Complete.html'):
        size_kb = round(os.path.getsize('SchemaLens-Complete.html') / 1024, 1)
        summary["executables"].append({
            "type": "Standalone HTML",
            "file": "SchemaLens-Complete.html",
            "size": f"{size_kb}KB",
            "platform": "Cross-platform",
            "features": ["Browser-based", "Offline", "Shareable"]
        })
    
    if os.path.exists('electron-dist'):
        summary["executables"].append({
            "type": "Electron App",
            "file": "electron-dist/",
            "size": "~100MB",
            "platform": "Windows/Mac/Linux",
            "features": ["Native app", "No browser needed", "Auto-updater"]
        })
    
    # Print summary
    print("📊 Build Results:")
    for exe in summary["executables"]:
        print(f"  ✅ {exe['type']}")
        print(f"     📁 {exe['file']}")
        print(f"     📊 {exe['size']}")
        print(f"     🖥️  {exe['platform']}")
        print(f"     🎯 {', '.join(exe['features'])}")
        print()
    
    # Save summary
    with open('build-summary.json', 'w') as f:
        json.dump(summary, f, indent=2)
    
    print("📋 Detailed summary saved to: build-summary.json")

def main():
    """Main build process"""
    print("🚀 Building All SchemaLens Executables")
    print("This will create multiple executable formats for different use cases")
    
    results = {}
    
    # Build Python executable
    results['python'] = build_python_exe()
    
    # Build standalone HTML
    results['html'] = build_standalone_html()
    
    # Build Electron app (optional, requires Node.js)
    results['electron'] = build_electron_app()
    
    # Create portable package
    results['portable'] = create_portable_package()
    
    # Create summary
    create_summary()
    
    # Final results
    print_header("Final Results")
    
    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    
    print(f"✅ Successfully built: {success_count}/{total_count} formats")
    
    for build_type, success in results.items():
        status = "✅" if success else "❌"
        print(f"  {status} {build_type.title()}")
    
    if success_count > 0:
        print("\n🎉 SchemaLens executables are ready for distribution!")
        print("\n📦 Distribution options:")
        print("  • dist/SchemaLens.exe - Windows executable")
        print("  • SchemaLens-Complete.html - Universal HTML file")
        print("  • SchemaLens-Portable/ - Multi-format package")
        print("  • electron-dist/ - Native desktop apps")
    else:
        print("\n❌ No executables were built successfully")
        print("Check the error messages above for troubleshooting")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
