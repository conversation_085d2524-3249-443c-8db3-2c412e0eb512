# SchemaLens Executable

## Quick Start

1. **Run directly**: Double-click `SchemaLens.exe`
2. **Install**: Run `install.bat` to install to your system

## What's Included

- `SchemaLens.exe` - Main application (standalone, no installation required)
- `install.bat` - Optional installer script
- `README.txt` - This file

## Features

- 📊 Interactive database schema explorer
- 🔍 Advanced search and filtering
- 📈 Comprehensive statistics and audit analysis
- 📋 ER diagram generation
- 🌙 Dark mode support
- 📱 Responsive design

## System Requirements

- Windows 7/8/10/11 (64-bit recommended)
- No additional software required
- Modern web browser (automatically opens)

## Usage

1. Run SchemaLens.exe
2. Click "Start SchemaLens" in the GUI
3. Upload your XML schema file
4. Explore your database structure!

## Troubleshooting

**Antivirus Warning**: Some antivirus software may flag the executable as suspicious. This is normal for PyInstaller-built applications. Add an exception if needed.

**Port Issues**: If port 8080 is busy, SchemaLens will automatically find another available port.

**Browser Issues**: If the browser doesn't open automatically, manually navigate to the URL shown in the application window.

## Support

For issues or questions, check the original SchemaLens documentation.

---
SchemaLens v1.0.0 - Database Schema Explorer
