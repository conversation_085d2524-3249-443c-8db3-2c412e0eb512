# SchemaLens Deployment Guide

This guide explains how to deploy SchemaLens in different environments.

## 🌐 Web Deployment

### Static File Hosting

SchemaLens is a client-side application that can be deployed to any static file hosting service:

#### GitHub Pages
1. Push your code to a GitHub repository
2. Go to Settings > Pages
3. Select source branch (usually `main`)
4. Your app will be available at `https://username.github.io/repository-name`

#### Netlify
1. Connect your GitHub repository to Netlify
2. Set build command: (leave empty for static files)
3. Set publish directory: `/` (root)
4. Deploy automatically on push

#### Vercel
1. Import your GitHub repository
2. No build configuration needed
3. Deploy with zero configuration

#### Apache/Nginx
Simply copy all files to your web server's document root:
```bash
cp -r * /var/www/html/
```

### CDN Deployment
For better performance, serve static assets via CDN:
- Upload files to AWS S3 + CloudFront
- Use Azure Blob Storage + CDN
- Deploy to Google Cloud Storage + CDN

## 🖥️ Desktop Application (Future)

### Tauri Setup
To package as a desktop application:

1. **Install Rust and Tauri <PERSON>**
   ```bash
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs/ | sh
   cargo install tauri-cli
   ```

2. **Initialize Tauri**
   ```bash
   cargo tauri init
   ```

3. **Configure tauri.conf.json**
   ```json
   {
     "build": {
       "distDir": "../dist",
       "devPath": "http://localhost:8080"
     },
     "tauri": {
       "allowlist": {
         "all": false,
         "fs": {
           "all": true,
           "readFile": true,
           "writeFile": true
         }
       }
     }
   }
   ```

4. **Build Desktop App**
   ```bash
   cargo tauri build
   ```

## 🐳 Docker Deployment

### Dockerfile
```dockerfile
FROM nginx:alpine

# Copy static files
COPY . /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### nginx.conf
```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        location / {
            try_files $uri $uri/ /index.html;
        }

        # Enable gzip compression
        gzip on;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    }
}
```

### Build and Run
```bash
docker build -t schema-lens .
docker run -p 8080:80 schema-lens
```

## ☁️ Cloud Deployment

### AWS S3 + CloudFront
1. Create S3 bucket with static website hosting
2. Upload files to S3
3. Create CloudFront distribution
4. Configure custom domain (optional)

### Google Cloud Platform
```bash
# Install gcloud CLI
gcloud app deploy

# app.yaml
runtime: python39
service: default
handlers:
- url: /
  static_files: index.html
  upload: index.html
- url: /(.*)
  static_files: \1
  upload: (.*)
```

### Azure Static Web Apps
1. Connect GitHub repository
2. Configure build settings:
   - App location: `/`
   - Output location: `/`
3. Deploy automatically

## 🔧 Environment Configuration

### Production Optimizations

1. **Enable Compression**
   - Gzip/Brotli compression for text files
   - Optimize images and assets

2. **Caching Headers**
   ```nginx
   location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
       expires 1y;
       add_header Cache-Control "public, immutable";
   }
   ```

3. **Security Headers**
   ```nginx
   add_header X-Frame-Options "SAMEORIGIN" always;
   add_header X-Content-Type-Options "nosniff" always;
   add_header Referrer-Policy "no-referrer-when-downgrade" always;
   ```

### Development Server
For development, use any of these options:

```bash
# Python
python -m http.server 8080

# Node.js
npx serve .

# PHP
php -S localhost:8080

# Live Server (VS Code extension)
# Right-click index.html > "Open with Live Server"
```

## 📱 Mobile Deployment

### Progressive Web App (PWA)
Add a manifest.json for PWA support:

```json
{
  "name": "SchemaLens",
  "short_name": "SchemaLens",
  "description": "Database schema explorer",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#3b82f6",
  "icons": [
    {
      "src": "icon-192.png",
      "sizes": "192x192",
      "type": "image/png"
    }
  ]
}
```

### Capacitor (Mobile Apps)
```bash
npm install @capacitor/core @capacitor/cli
npx cap init
npx cap add ios
npx cap add android
npx cap run ios
npx cap run android
```

## 🚀 Performance Tips

1. **Lazy Loading**: Load Mermaid.js only when needed
2. **Code Splitting**: Split large schemas into chunks
3. **Caching**: Cache parsed schemas in localStorage
4. **Compression**: Use gzip/brotli for text assets
5. **CDN**: Serve static assets from CDN

## 🔍 Monitoring

### Analytics
Add Google Analytics or similar:
```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
```

### Error Tracking
Add Sentry or similar error tracking:
```javascript
import * as Sentry from "@sentry/browser";
Sentry.init({ dsn: "YOUR_DSN" });
```

## 🛡️ Security Considerations

1. **Content Security Policy (CSP)**
2. **HTTPS Only** in production
3. **Input Validation** for XML files
4. **File Size Limits** for uploads
5. **Rate Limiting** if needed

## 📋 Deployment Checklist

- [ ] Test all features work correctly
- [ ] Verify XML parsing with sample files
- [ ] Check responsive design on mobile
- [ ] Test diagram generation and export
- [ ] Validate search functionality
- [ ] Ensure proper error handling
- [ ] Configure caching headers
- [ ] Set up monitoring/analytics
- [ ] Test performance with large schemas
- [ ] Verify security headers

---

Choose the deployment method that best fits your needs and infrastructure! 🚀
