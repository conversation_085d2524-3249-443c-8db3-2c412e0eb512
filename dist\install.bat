@echo off
title SchemaLens Installer
echo.
echo ========================================
echo   SchemaLens Database Schema Explorer
echo ========================================
echo.
echo This will install SchemaLens to your system.
echo.
pause

set "INSTALL_DIR=%USERPROFILE%\SchemaLens"
echo Creating installation directory: %INSTALL_DIR%
mkdir "%INSTALL_DIR%" 2>nul

echo Copying files...
copy "SchemaLens.exe" "%INSTALL_DIR%\" >nul
if errorlevel 1 (
    echo Error: Failed to copy executable
    pause
    exit /b 1
)

echo Creating desktop shortcut...
set "SHORTCUT=%USERPROFILE%\Desktop\SchemaLens.lnk"
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\SchemaLens.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Database Schema Explorer'; $Shortcut.Save()"

echo Creating start menu entry...
set "STARTMENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs"
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%STARTMENU%\SchemaLens.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\SchemaLens.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Database Schema Explorer'; $Shortcut.Save()"

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo SchemaLens has been installed to: %INSTALL_DIR%
echo Desktop shortcut created: %USERPROFILE%\Desktop\SchemaLens.lnk
echo Start menu entry created
echo.
echo You can now run SchemaLens from:
echo - Desktop shortcut
echo - Start menu
echo - Command: "%INSTALL_DIR%\SchemaLens.exe"
echo.
pause
