// SchemaLens Service Worker
// Provides offline functionality and caching

const CACHE_NAME = 'schemalens-v1.0.0';
const STATIC_CACHE_NAME = 'schemalens-static-v1.0.0';

// Files to cache for offline use
const STATIC_FILES = [
  '/',
  '/index.html',
  '/src/app.js',
  '/sample-schema.xml',
  '/manifest.json',
  // CDN resources (cached when accessed)
  'https://unpkg.com/react@18/umd/react.development.js',
  'https://unpkg.com/react-dom@18/umd/react-dom.development.js',
  'https://cdn.tailwindcss.com',
  'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js'
];

// Install event - cache static resources
self.addEventListener('install', event => {
  console.log('SchemaLens Service Worker installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then(cache => {
        console.log('Caching static files...');
        return cache.addAll(STATIC_FILES.filter(url => !url.startsWith('http')));
      })
      .then(() => {
        console.log('Static files cached successfully');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('Error caching static files:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('SchemaLens Service Worker activating...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== CACHE_NAME && cacheName !== STATIC_CACHE_NAME) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - serve from cache when offline
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Handle different types of requests
  if (url.origin === location.origin) {
    // Same origin - use cache-first strategy
    event.respondWith(cacheFirst(request));
  } else if (url.hostname.includes('cdn') || url.hostname.includes('unpkg')) {
    // CDN resources - use stale-while-revalidate
    event.respondWith(staleWhileRevalidate(request));
  } else {
    // Other external resources - network first
    event.respondWith(networkFirst(request));
  }
});

// Cache-first strategy
async function cacheFirst(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.error('Cache-first strategy failed:', error);
    return new Response('Offline - Resource not available', { status: 503 });
  }
}

// Network-first strategy
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    return new Response('Offline - Resource not available', { status: 503 });
  }
}

// Stale-while-revalidate strategy
async function staleWhileRevalidate(request) {
  const cache = await caches.open(CACHE_NAME);
  const cachedResponse = await cache.match(request);
  
  const fetchPromise = fetch(request).then(networkResponse => {
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  }).catch(() => cachedResponse);
  
  return cachedResponse || fetchPromise;
}

// Handle background sync for file uploads
self.addEventListener('sync', event => {
  if (event.tag === 'schema-upload') {
    event.waitUntil(handleSchemaUpload());
  }
});

async function handleSchemaUpload() {
  // Handle queued schema uploads when back online
  console.log('Processing queued schema uploads...');
  // Implementation would depend on how uploads are queued
}

// Handle push notifications (future feature)
self.addEventListener('push', event => {
  if (event.data) {
    const data = event.data.json();
    const options = {
      body: data.body || 'SchemaLens notification',
      icon: '/icon-192.png',
      badge: '/badge-72.png',
      tag: 'schemalens-notification',
      data: data.data || {}
    };
    
    event.waitUntil(
      self.registration.showNotification(data.title || 'SchemaLens', options)
    );
  }
});

// Handle notification clicks
self.addEventListener('notificationclick', event => {
  event.notification.close();
  
  event.waitUntil(
    clients.openWindow('/')
  );
});

// Provide offline page for navigation requests
self.addEventListener('fetch', event => {
  if (event.request.mode === 'navigate') {
    event.respondWith(
      fetch(event.request)
        .catch(() => {
          return caches.match('/') || caches.match('/index.html');
        })
    );
  }
});

// Log service worker events
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({
      version: CACHE_NAME,
      cached: STATIC_FILES.length
    });
  }
});

console.log('SchemaLens Service Worker loaded');
