import { useState, useMemo } from 'react'
import TableList from './TableList'
import TableDetail from './TableDetail'

function TableExplorer({ schemaData, selectedTable, onTableSelect, searchQuery }) {
  const [sidebarWidth, setSidebarWidth] = useState(320)

  const filteredTables = useMemo(() => {
    if (!schemaData?.tables || !searchQuery) {
      return schemaData?.tables || []
    }

    return schemaData.tables.filter(table => {
      // Search in table name
      if (table.name.toLowerCase().includes(searchQuery.toLowerCase())) {
        return true
      }
      
      // Search in column names
      return table.columns.some(column => 
        column.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
    })
  }, [schemaData?.tables, searchQuery])

  const handleResize = (e) => {
    const startX = e.clientX
    const startWidth = sidebarWidth

    const handleMouseMove = (e) => {
      const newWidth = startWidth + (e.clientX - startX)
      setSidebarWidth(Math.max(250, Math.min(600, newWidth)))
    }

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }

  return (
    <div className="flex-1 flex">
      {/* Sidebar - Table List */}
      <div 
        className="bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col"
        style={{ width: `${sidebarWidth}px` }}
      >
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Tables ({filteredTables.length})
          </h2>
          {searchQuery && (
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Filtered by: "{searchQuery}"
            </p>
          )}
        </div>
        
        <div className="flex-1 overflow-hidden">
          <TableList
            tables={filteredTables}
            selectedTable={selectedTable}
            onTableSelect={onTableSelect}
            searchQuery={searchQuery}
          />
        </div>
      </div>

      {/* Resize Handle */}
      <div
        className="w-1 bg-gray-200 dark:bg-gray-700 cursor-col-resize hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
        onMouseDown={handleResize}
      />

      {/* Main Content - Table Detail */}
      <div className="flex-1 flex flex-col">
        {selectedTable ? (
          <TableDetail
            table={selectedTable}
            schemaData={schemaData}
            searchQuery={searchQuery}
          />
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
            <div className="text-center">
              <div className="text-6xl mb-4">🗂️</div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Select a Table
              </h3>
              <p className="text-gray-600 dark:text-gray-400 max-w-md">
                Choose a table from the sidebar to view its structure, columns, 
                relationships, and constraints.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default TableExplorer
