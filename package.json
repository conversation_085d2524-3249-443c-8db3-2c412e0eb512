{"name": "schema-lens", "version": "1.0.0", "description": "A cross-platform desktop application for exploring XML-based SQL schemas", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "serve": "python -m http.server 8080"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8", "tailwindcss": "^3.4.0", "postcss": "^8.4.32", "autoprefixer": "^10.4.16"}, "keywords": ["react", "xml", "schema", "database"], "author": "", "license": "MIT"}