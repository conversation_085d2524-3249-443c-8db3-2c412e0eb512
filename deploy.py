#!/usr/bin/env python3
"""
SchemaLens Deployment Script
Builds and packages SchemaLens for multiple distribution methods
"""

import os
import shutil
import zipfile
import json
import datetime
from pathlib import Path

def create_directory(path):
    """Create directory if it doesn't exist"""
    Path(path).mkdir(parents=True, exist_ok=True)

def copy_files(src_files, dest_dir):
    """Copy multiple files to destination directory"""
    create_directory(dest_dir)
    for src in src_files:
        if os.path.exists(src):
            if os.path.isfile(src):
                shutil.copy2(src, dest_dir)
            else:
                shutil.copytree(src, os.path.join(dest_dir, os.path.basename(src)), dirs_exist_ok=True)

def create_zip(source_dir, zip_path):
    """Create a zip file from directory"""
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(source_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_path = os.path.relpath(file_path, source_dir)
                zipf.write(file_path, arc_path)

def build_standalone():
    """Build standalone HTML version"""
    print("📦 Building Standalone HTML...")
    
    # Run the standalone build script
    os.system("python build_standalone.py")
    
    # Copy to dist
    dist_dir = "dist/standalone"
    create_directory(dist_dir)
    
    if os.path.exists("SchemaLens-Complete.html"):
        shutil.copy2("SchemaLens-Complete.html", dist_dir)
        print(f"✅ Standalone HTML: {dist_dir}/SchemaLens-Complete.html")
    
    return dist_dir

def build_pwa():
    """Build Progressive Web App version"""
    print("📱 Building PWA...")
    
    dist_dir = "dist/pwa"
    
    # Core files for PWA
    pwa_files = [
        "index.html",
        "src/app.js",
        "sample-schema.xml",
        "manifest.json",
        "sw.js"
    ]
    
    copy_files(pwa_files, dist_dir)
    
    # Create a simple server script for PWA
    server_script = """#!/usr/bin/env python3
import http.server
import socketserver
import webbrowser
import os

PORT = 8080
os.chdir(os.path.dirname(os.path.abspath(__file__)))

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Service-Worker-Allowed', '/')
        super().end_headers()

with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
    print(f"SchemaLens PWA running at http://localhost:{PORT}")
    print("Install as app using browser's install button")
    webbrowser.open(f"http://localhost:{PORT}")
    httpd.serve_forever()
"""
    
    with open(f"{dist_dir}/run_pwa.py", "w", encoding='utf-8') as f:
        f.write(server_script)
    
    print(f"✅ PWA: {dist_dir}/")
    return dist_dir

def build_static():
    """Build static website version"""
    print("🌐 Building Static Website...")
    
    dist_dir = "dist/static"
    
    # All files for static hosting
    static_files = [
        "index.html",
        "src",
        "sample-schema.xml",
        "manifest.json",
        "sw.js",
        "README.md",
        "DEPLOYMENT.md"
    ]
    
    copy_files(static_files, dist_dir)
    
    # Create deployment instructions
    deploy_instructions = """# SchemaLens Static Deployment

## Quick Deploy Options:

### GitHub Pages
1. Push this folder to a GitHub repository
2. Go to Settings > Pages
3. Select source branch
4. Access at: https://username.github.io/repository-name

### Netlify
1. Drag and drop this folder to netlify.com/drop
2. Or connect to GitHub repository
3. Automatic deployment on push

### Vercel
1. Install Vercel CLI: npm i -g vercel
2. Run: vercel --prod
3. Or connect GitHub repository

### Apache/Nginx
1. Copy all files to web server document root
2. Ensure proper MIME types for .json and .js files
3. Enable HTTPS for PWA features

### Python Server (Local)
```bash
python -m http.server 8080
```

### Node.js Server (Local)
```bash
npx serve .
```
"""
    
    with open(f"{dist_dir}/DEPLOY.md", "w", encoding='utf-8') as f:
        f.write(deploy_instructions)
    
    print(f"✅ Static Website: {dist_dir}/")
    return dist_dir

def build_portable():
    """Build portable executable version"""
    print("💼 Building Portable Version...")
    
    dist_dir = "dist/portable"
    
    # Copy all files
    portable_files = [
        "index.html",
        "src",
        "sample-schema.xml",
        "manifest.json",
        "README.md"
    ]
    
    copy_files(portable_files, dist_dir)
    
    # Create portable launcher scripts
    
    # Windows batch file
    windows_launcher = """@echo off
title SchemaLens - Database Schema Explorer
echo Starting SchemaLens...
echo.
echo SchemaLens will open in your default browser
echo Server running at http://localhost:8080
echo Press Ctrl+C to stop the server
echo.

python -m http.server 8080
pause
"""
    
    with open(f"{dist_dir}/SchemaLens.bat", "w", encoding='utf-8') as f:
        f.write(windows_launcher)

    # Linux/Mac shell script
    unix_launcher = """#!/bin/bash
echo "Starting SchemaLens..."
echo ""
echo "Server running at http://localhost:8080"
echo "Press Ctrl+C to stop the server"
echo ""

# Try to open in browser
if command -v xdg-open > /dev/null; then
    xdg-open http://localhost:8080
elif command -v open > /dev/null; then
    open http://localhost:8080
fi

python3 -m http.server 8080
"""

    with open(f"{dist_dir}/schemalens.sh", "w", encoding='utf-8') as f:
        f.write(unix_launcher)
    
    # Make shell script executable
    os.chmod(f"{dist_dir}/schemalens.sh", 0o755)
    
    print(f"✅ Portable: {dist_dir}/")
    return dist_dir

def create_release_package():
    """Create complete release package"""
    print("📦 Creating Release Package...")
    
    # Build all versions
    standalone_dir = build_standalone()
    pwa_dir = build_pwa()
    static_dir = build_static()
    portable_dir = build_portable()
    
    # Create release info
    release_info = {
        "name": "SchemaLens",
        "version": "1.0.0",
        "build_date": datetime.datetime.now().isoformat(),
        "distributions": {
            "standalone": "Single HTML file - works offline",
            "pwa": "Progressive Web App - installable",
            "static": "Static website - for web hosting",
            "portable": "Portable version - run anywhere"
        },
        "requirements": {
            "browser": "Modern web browser (Chrome, Firefox, Safari, Edge)",
            "python": "Python 3.6+ (for local server)",
            "internet": "Optional (for CDN resources)"
        }
    }
    
    with open("dist/release-info.json", "w", encoding='utf-8') as f:
        json.dump(release_info, f, indent=2)

    # Create main README
    main_readme = """# SchemaLens v1.0.0 - Distribution Package

## What's Included

### Quick Start Options:

1. **Standalone HTML** (`standalone/SchemaLens-Complete.html`)
   - Single file, works offline
   - Just open in any browser
   - Perfect for sharing via email

2. **Progressive Web App** (`pwa/`)
   - Installable as desktop/mobile app
   - Run: `python pwa/run_pwa.py`
   - Install using browser's install button

3. **Static Website** (`static/`)
   - Deploy to any web server
   - See `static/DEPLOY.md` for options
   - GitHub Pages, Netlify, Vercel ready

4. **Portable Version** (`portable/`)
   - Windows: Double-click `SchemaLens.bat`
   - Mac/Linux: Run `./schemalens.sh`
   - No installation required

## Features

- XML schema upload and parsing
- Interactive table explorer with advanced filtering
- Comprehensive statistics and audit analysis
- ER diagram generation with Mermaid
- Dark mode support
- Mobile responsive design
- Offline functionality (PWA)

## System Requirements

- Modern web browser (Chrome, Firefox, Safari, Edge)
- Python 3.6+ (for local server versions)
- No additional dependencies required

## Support

For issues or questions, check the README.md in each distribution folder.

---
Built with React, Tailwind CSS, and Mermaid.js
"""

    with open("dist/README.md", "w", encoding='utf-8') as f:
        f.write(main_readme)
    
    # Create zip packages
    print("🗜️ Creating ZIP packages...")
    
    create_zip("dist/standalone", "dist/SchemaLens-Standalone.zip")
    create_zip("dist/pwa", "dist/SchemaLens-PWA.zip")
    create_zip("dist/static", "dist/SchemaLens-Static.zip")
    create_zip("dist/portable", "dist/SchemaLens-Portable.zip")
    create_zip("dist", "SchemaLens-Complete-v1.0.0.zip")
    
    print("\n🎉 Build Complete!")
    print("\n📦 Distribution Packages:")
    print("   📁 dist/standalone/ - Single HTML file")
    print("   📁 dist/pwa/ - Progressive Web App")
    print("   📁 dist/static/ - Static website")
    print("   📁 dist/portable/ - Portable executables")
    print("\n📦 ZIP Packages:")
    print("   📄 SchemaLens-Complete-v1.0.0.zip - Everything")
    print("   📄 dist/SchemaLens-Standalone.zip")
    print("   📄 dist/SchemaLens-PWA.zip")
    print("   📄 dist/SchemaLens-Static.zip")
    print("   📄 dist/SchemaLens-Portable.zip")

if __name__ == "__main__":
    # Clean previous builds
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    
    create_release_package()
    print("\n✨ Ready to share SchemaLens with the world!")
