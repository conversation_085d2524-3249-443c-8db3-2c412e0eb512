#!/usr/bin/env python3
"""
Create a simple icon for SchemaLens
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

def create_simple_icon():
    """Create a simple icon using PIL"""
    if not PIL_AVAILABLE:
        print("PIL not available, skipping icon creation")
        return False
    
    # Create a 256x256 image with blue background
    size = 256
    img = Image.new('RGBA', (size, size), (59, 130, 246, 255))  # Blue background
    draw = ImageDraw.Draw(img)
    
    # Draw a simple database/chart icon
    # Draw database cylinders
    cylinder_width = 120
    cylinder_height = 40
    x_center = size // 2
    y_start = 60
    
    # Draw three database cylinders
    for i in range(3):
        y = y_start + i * 50
        # Cylinder body
        draw.rectangle([x_center - cylinder_width//2, y, x_center + cylinder_width//2, y + cylinder_height], 
                      fill=(255, 255, 255, 255))
        # Top ellipse
        draw.ellipse([x_center - cylinder_width//2, y - 10, x_center + cylinder_width//2, y + 10], 
                    fill=(255, 255, 255, 255))
        # Bottom ellipse
        draw.ellipse([x_center - cylinder_width//2, y + cylinder_height - 10, x_center + cylinder_width//2, y + cylinder_height + 10], 
                    fill=(255, 255, 255, 255))
    
    # Add chart lines
    chart_y = 200
    for i in range(5):
        x = 80 + i * 20
        height = 20 + i * 10
        draw.rectangle([x, chart_y - height, x + 15, chart_y], fill=(34, 197, 94, 255))  # Green bars
    
    # Save as ICO
    try:
        # Create multiple sizes for ICO
        sizes = [16, 32, 48, 64, 128, 256]
        images = []
        
        for ico_size in sizes:
            resized = img.resize((ico_size, ico_size), Image.Resampling.LANCZOS)
            images.append(resized)
        
        # Save as ICO file
        img.save('schemalens.ico', format='ICO', sizes=[(s, s) for s in sizes])
        print("✅ Icon created: schemalens.ico")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create ICO: {e}")
        return False

def create_fallback_icon():
    """Create a fallback icon without PIL"""
    # Create a minimal ICO file with just the header
    # This is a very basic 16x16 icon
    ico_data = bytes([
        # ICO header
        0x00, 0x00,  # Reserved
        0x01, 0x00,  # Type (1 = ICO)
        0x01, 0x00,  # Number of images
        
        # Image directory entry
        0x10,        # Width (16)
        0x10,        # Height (16)
        0x00,        # Color count (0 = no palette)
        0x00,        # Reserved
        0x01, 0x00,  # Color planes
        0x20, 0x00,  # Bits per pixel (32)
        0x00, 0x04, 0x00, 0x00,  # Image size (1024 bytes)
        0x16, 0x00, 0x00, 0x00,  # Image offset (22 bytes)
        
        # Bitmap data (16x16 RGBA)
        # This creates a simple blue square with white center
    ])
    
    # Add bitmap data (simplified)
    bitmap_data = bytearray(1024)  # 16x16x4 bytes (RGBA)
    
    # Fill with blue background and white center
    for y in range(16):
        for x in range(16):
            offset = (y * 16 + x) * 4
            if 4 <= x <= 11 and 4 <= y <= 11:
                # White center
                bitmap_data[offset:offset+4] = [255, 255, 255, 255]  # BGRA
            else:
                # Blue background
                bitmap_data[offset:offset+4] = [246, 130, 59, 255]  # BGRA
    
    ico_data += bitmap_data
    
    try:
        with open('schemalens.ico', 'wb') as f:
            f.write(ico_data)
        print("✅ Fallback icon created: schemalens.ico")
        return True
    except Exception as e:
        print(f"❌ Failed to create fallback icon: {e}")
        return False

if __name__ == "__main__":
    print("🎨 Creating SchemaLens icon...")
    
    if create_simple_icon():
        print("Icon created successfully with PIL")
    elif create_fallback_icon():
        print("Fallback icon created successfully")
    else:
        print("Failed to create icon")
