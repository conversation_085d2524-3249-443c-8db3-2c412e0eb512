export async function parseXMLSchema(xmlText) {
  try {
    // Use browser's built-in DOMParser
    const parser = new DOMParser()
    const xmlDoc = parser.parseFromString(xmlText, 'text/xml')

    // Check for parsing errors
    const parseError = xmlDoc.querySelector('parsererror')
    if (parseError) {
      throw new Error('XML parsing error: ' + parseError.textContent)
    }

    // Extract database schema information
    const schema = extractSchemaData(xmlDoc)
    return schema
  } catch (error) {
    console.error('XML parsing error:', error)
    throw new Error('Failed to parse XML schema')
  }
}

function extractSchemaData(xmlDoc) {
  const schema = {
    tables: [],
    relationships: []
  }

  // Try to find tables in common XML schema formats
  let tableElements = xmlDoc.querySelectorAll('table')

  // If no direct table elements, try other common patterns
  if (tableElements.length === 0) {
    tableElements = xmlDoc.querySelectorAll('database > table, schema > table, tables > table')
  }

  tableElements.forEach(tableElement => {
    const table = parseTable(tableElement)
    if (table) {
      schema.tables.push(table)

      // Extract relationships
      table.columns.forEach(column => {
        if (column.foreignKey) {
          schema.relationships.push({
            from: table.name,
            fromColumn: column.name,
            to: column.foreignKey.table,
            toColumn: column.foreignKey.column,
            type: 'foreign_key'
          })
        }
      })
    }
  })

  return schema
}

function parseTable(tableElement) {
  const tableName = tableElement.getAttribute('name')
  if (!tableName) {
    return null
  }

  const table = {
    name: tableName,
    columns: [],
    primaryKeys: [],
    foreignKeys: [],
    constraints: [],
    description: tableElement.getAttribute('description') || ''
  }

  // Parse columns
  const columnElements = tableElement.querySelectorAll('column')

  columnElements.forEach(columnElement => {
    const column = parseColumn(columnElement)
    if (column) {
      table.columns.push(column)

      if (column.isPrimaryKey) {
        table.primaryKeys.push(column.name)
      }

      if (column.foreignKey) {
        table.foreignKeys.push({
          column: column.name,
          referencedTable: column.foreignKey.table,
          referencedColumn: column.foreignKey.column
        })
      }
    }
  })

  return table
}

function parseColumn(columnElement) {
  const columnName = columnElement.getAttribute('name')
  if (!columnName) {
    return null
  }

  const column = {
    name: columnName,
    type: columnElement.getAttribute('type') || 'varchar',
    nullable: columnElement.getAttribute('nullable') !== 'false',
    isPrimaryKey: columnElement.getAttribute('primaryKey') === 'true',
    isUnique: columnElement.getAttribute('unique') === 'true',
    defaultValue: columnElement.getAttribute('default') || columnElement.getAttribute('defaultValue') || '',
    description: columnElement.getAttribute('description') || '',
    foreignKey: null
  }

  // Parse foreign key reference
  const foreignKeyRef = columnElement.getAttribute('foreignKey')
  if (foreignKeyRef) {
    const fkParts = foreignKeyRef.split('.')
    if (fkParts.length === 2) {
      column.foreignKey = {
        table: fkParts[0],
        column: fkParts[1]
      }
    }
  }

  return column
}

export function generateMermaidDiagram(schema) {
  if (!schema || !schema.tables || schema.tables.length === 0) {
    return 'graph TD\n    A[No tables found]'
  }

  let mermaidCode = 'erDiagram\n'

  // Add tables and their columns
  schema.tables.forEach(table => {
    mermaidCode += `    ${table.name} {\n`
    
    table.columns.forEach(column => {
      let columnLine = `        ${column.type} ${column.name}`
      
      if (column.isPrimaryKey) {
        columnLine += ' PK'
      }
      if (column.foreignKey) {
        columnLine += ' FK'
      }
      if (!column.nullable) {
        columnLine += ' "NOT NULL"'
      }
      if (column.isUnique) {
        columnLine += ' "UNIQUE"'
      }
      
      mermaidCode += columnLine + '\n'
    })
    
    mermaidCode += '    }\n\n'
  })

  // Add relationships
  schema.relationships.forEach(rel => {
    mermaidCode += `    ${rel.from} ||--o{ ${rel.to} : "${rel.fromColumn} -> ${rel.toColumn}"\n`
  })

  return mermaidCode
}
