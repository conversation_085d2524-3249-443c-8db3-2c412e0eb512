import { useMemo } from 'react'

function TableList({ tables, selectedTable, onTableSelect, searchQuery }) {
  const highlightText = (text, query) => {
    if (!query) return text
    
    const regex = new RegExp(`(${query})`, 'gi')
    const parts = text.split(regex)
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <span key={index} className="search-highlight">
          {part}
        </span>
      ) : part
    )
  }

  const getTableMatchInfo = (table, query) => {
    if (!query) return null
    
    const matchingColumns = table.columns.filter(column =>
      column.name.toLowerCase().includes(query.toLowerCase())
    )
    
    return matchingColumns.length > 0 ? matchingColumns : null
  }

  return (
    <div className="custom-scrollbar overflow-y-auto h-full">
      {tables.length === 0 ? (
        <div className="p-4 text-center text-gray-500 dark:text-gray-400">
          {searchQuery ? 'No tables match your search' : 'No tables found'}
        </div>
      ) : (
        <div className="p-2">
          {tables.map((table) => {
            const isSelected = selectedTable?.name === table.name
            const matchingColumns = getTableMatchInfo(table, searchQuery)
            
            return (
              <div
                key={table.name}
                className={`
                  p-3 mb-2 rounded-lg cursor-pointer transition-all duration-200
                  ${isSelected 
                    ? 'bg-blue-100 dark:bg-blue-900 border-2 border-blue-300 dark:border-blue-600' 
                    : 'bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 border-2 border-transparent'
                  }
                `}
                onClick={() => onTableSelect(table)}
              >
                <div className="flex items-center justify-between">
                  <h3 className={`font-medium ${isSelected ? 'text-blue-900 dark:text-blue-100' : 'text-gray-900 dark:text-white'}`}>
                    {highlightText(table.name, searchQuery)}
                  </h3>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    isSelected 
                      ? 'bg-blue-200 dark:bg-blue-800 text-blue-800 dark:text-blue-200' 
                      : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300'
                  }`}>
                    {table.columns.length} cols
                  </span>
                </div>
                
                {table.description && (
                  <p className={`text-sm mt-1 ${isSelected ? 'text-blue-700 dark:text-blue-300' : 'text-gray-600 dark:text-gray-400'}`}>
                    {table.description}
                  </p>
                )}
                
                {/* Show primary keys */}
                {table.primaryKeys.length > 0 && (
                  <div className="mt-2">
                    <span className={`text-xs font-medium ${isSelected ? 'text-blue-800 dark:text-blue-200' : 'text-gray-700 dark:text-gray-300'}`}>
                      PK: {table.primaryKeys.join(', ')}
                    </span>
                  </div>
                )}
                
                {/* Show matching columns when searching */}
                {matchingColumns && (
                  <div className="mt-2">
                    <span className={`text-xs ${isSelected ? 'text-blue-700 dark:text-blue-300' : 'text-gray-600 dark:text-gray-400'}`}>
                      Matching columns: {matchingColumns.map(col => highlightText(col.name, searchQuery)).reduce((prev, curr, index) => 
                        index === 0 ? [curr] : [...prev, ', ', curr], []
                      )}
                    </span>
                  </div>
                )}
                
                {/* Show foreign key count */}
                {table.foreignKeys.length > 0 && (
                  <div className="mt-1">
                    <span className={`text-xs ${isSelected ? 'text-blue-700 dark:text-blue-300' : 'text-gray-600 dark:text-gray-400'}`}>
                      {table.foreignKeys.length} foreign key{table.foreignKeys.length !== 1 ? 's' : ''}
                    </span>
                  </div>
                )}
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}

export default TableList
