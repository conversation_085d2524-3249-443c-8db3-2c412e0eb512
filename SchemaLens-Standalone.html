<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SchemaLens - Database Schema Explorer</title>
    <meta name="description" content="Interactive database schema explorer and analyzer">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>">
    
    <!-- React and ReactDOM from CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Mermaid for diagrams -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    
    <style>
        /* Custom styles */
        .search-highlight {
            background-color: #fef08a;
            font-weight: 600;
            padding: 1px 2px;
            border-radius: 2px;
        }
        
        .custom-scrollbar {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e1 #f1f5f9;
        }
        
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        /* Dark mode scrollbar */
        .dark .custom-scrollbar {
            scrollbar-color: #4b5563 #374151;
        }
        
        .dark .custom-scrollbar::-webkit-scrollbar-track {
            background: #374151;
        }
        
        .dark .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #4b5563;
        }
        
        .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #6b7280;
        }
        
        /* Animation for loading states */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        /* Print styles */
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                font-size: 12px;
            }
            
            .print-break {
                page-break-before: always;
            }
        }
        
        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .search-highlight {
                background-color: #000;
                color: #fff;
            }
        }
        
        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .transition-all,
            .transition-colors {
                transition: none !important;
            }
            
            .animate-pulse {
                animation: none !important;
            }
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900">
    <div id="root"></div>
    
    <!-- Application JavaScript -->
    <script>
        // SchemaLens Application - Standalone Version
        // All functionality embedded in this single file

        // Copy the entire content from src/app.js here
        // This is a placeholder - we'll use a build script to inject the actual content

        // For now, let's include a minimal version to test
        console.log('SchemaLens Standalone Edition Loading...');

        // We'll replace this with the full app.js content
        document.addEventListener('DOMContentLoaded', function() {
            const root = document.getElementById('root');
            if (root) {
                root.innerHTML = `
                    <div class="h-screen flex items-center justify-center">
                        <div class="text-center">
                            <div class="text-6xl mb-4">📊</div>
                            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">SchemaLens</h1>
                            <p class="text-gray-600 dark:text-gray-400 mb-6">Standalone Edition</p>
                            <p class="text-sm text-gray-500">Loading application...</p>
                        </div>
                    </div>
                `;
            }
        });
    </script>
    
    <!-- Footer with credits -->
    <div class="no-print fixed bottom-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 text-xs text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-2">
            <span>📊</span>
            <span><strong>SchemaLens</strong> v1.0</span>
            <span>•</span>
            <span>Standalone Edition</span>
        </div>
    </div>
</body>
</html>
