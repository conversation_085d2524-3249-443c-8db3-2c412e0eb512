import { useState } from 'react'
import './App.css'
import FileUpload from './components/FileUpload'
import TableExplorer from './components/TableExplorer'
import DiagramView from './components/DiagramView'
import SearchBar from './components/SearchBar'

function App() {
  const [schemaData, setSchemaData] = useState(null)
  const [selectedTable, setSelectedTable] = useState(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [currentView, setCurrentView] = useState('explorer') // 'explorer' or 'diagram'

  return (
    <div className="h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Top Bar */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">
              SchemaLens
            </h1>
            <FileUpload onSchemaLoad={setSchemaData} />
          </div>
          
          <div className="flex items-center space-x-4">
            <SearchBar 
              searchQuery={searchQuery}
              onSearchChange={setSearchQuery}
              schemaData={schemaData}
            />
            
            <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              <button
                onClick={() => setCurrentView('explorer')}
                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                  currentView === 'explorer'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                Explorer
              </button>
              <button
                onClick={() => setCurrentView('diagram')}
                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                  currentView === 'diagram'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                Diagram
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {schemaData ? (
          currentView === 'explorer' ? (
            <TableExplorer
              schemaData={schemaData}
              selectedTable={selectedTable}
              onTableSelect={setSelectedTable}
              searchQuery={searchQuery}
            />
          ) : (
            <DiagramView schemaData={schemaData} />
          )
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="text-6xl mb-4">📊</div>
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-2">
                Welcome to SchemaLens
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md">
                Upload an XML schema file to start exploring your database structure
                with interactive diagrams and detailed table views.
              </p>
              <FileUpload onSchemaLoad={setSchemaData} />
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default App
