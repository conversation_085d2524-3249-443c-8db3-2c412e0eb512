#!/usr/bin/env python3
"""
Build Script for SchemaLens Standalone HTML
Creates a single self-contained HTML file with all dependencies
"""

import os
import datetime
import re

def build_standalone():
    print("🔨 Building SchemaLens Standalone Edition...")
    
    try:
        # Read the template HTML file
        with open('SchemaLens-Standalone.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Read the main application JavaScript
        with open('src/app.js', 'r', encoding='utf-8') as f:
            app_js_content = f.read()
        
        # Read the sample schema for embedding
        with open('sample-schema.xml', 'r', encoding='utf-8') as f:
            sample_schema_content = f.read()
        
        # Escape the sample schema content for embedding in JavaScript
        escaped_sample_schema = (sample_schema_content
                                .replace('\\', '\\\\')
                                .replace('`', '\\`')
                                .replace('$', '\\$'))
        
        # Create the complete JavaScript content
        complete_js_content = f"""
        // SchemaLens Standalone Edition - Complete Application
        // Generated on: {datetime.datetime.now().isoformat()}
        
        // Embedded sample schema
        const EMBEDDED_SAMPLE_SCHEMA = `{escaped_sample_schema}`;
        
        // Main application code
        {app_js_content}
        
        // Enhanced standalone features
        window.SchemaLens = {{
            loadSampleSchema: function() {{
                try {{
                    const parsedSchema = parseXMLSchema(EMBEDDED_SAMPLE_SCHEMA);
                    console.log('Sample schema loaded:', parsedSchema);
                    return parsedSchema;
                }} catch (error) {{
                    console.error('Error loading sample:', error);
                    throw error;
                }}
            }},
            
            version: '1.0.0',
            buildDate: '{datetime.datetime.now().isoformat()}',
            
            exportSchema: function(schema, format = 'json') {{
                if (format === 'json') {{
                    return JSON.stringify(schema, null, 2);
                }} else if (format === 'markdown') {{
                    let md = '# Database Schema\\n\\n';
                    schema.tables.forEach(table => {{
                        md += `## ${{table.name}}\\n\\n`;
                        if (table.description) md += `${{table.description}}\\n\\n`;
                        md += '| Column | Type | Constraints |\\n|--------|------|-------------|\\n';
                        table.columns.forEach(col => {{
                            const constraints = [];
                            if (col.isPrimaryKey) constraints.push('PK');
                            if (col.foreignKey) constraints.push('FK');
                            if (!col.nullable) constraints.push('NOT NULL');
                            if (col.isUnique) constraints.push('UNIQUE');
                            md += `| ${{col.name}} | ${{col.type}} | ${{constraints.join(', ')}} |\\n`;
                        }});
                        md += '\\n';
                    }});
                    return md;
                }}
                return 'Unsupported format';
            }}
        }};
        
        // Auto-initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {{
            console.log('SchemaLens Standalone Edition v1.0.0 loaded');
            console.log('Use window.SchemaLens.loadSampleSchema() to load the embedded sample');
        }});
        """
        
        # Replace the placeholder JavaScript with the complete content
        js_pattern = r'<!-- Application JavaScript -->\s*<script>[\s\S]*?</script>'
        replacement = f"""<!-- Application JavaScript -->
    <script>
{complete_js_content}
    </script>"""
        
        html_content = re.sub(js_pattern, replacement, html_content)
        
        # Add build information
        build_info = f"""
    <!-- Build Information -->
    <!-- Generated: {datetime.datetime.now().isoformat()} -->
    <!-- Version: 1.0.0 -->
    <!-- Builder: Python Script -->
    """
        
        html_content = html_content.replace('</head>', f'{build_info}</head>')
        
        # Write the complete standalone file
        output_path = 'SchemaLens-Complete.html'
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        # Get file size
        file_size_kb = round(os.path.getsize(output_path) / 1024)
        
        print("✅ Build completed successfully!")
        print(f"📁 Output: {output_path}")
        print(f"📊 Size: {file_size_kb}KB")
        print("")
        print("🚀 Usage:")
        print("   1. Open SchemaLens-Complete.html in any modern web browser")
        print("   2. No internet connection required (all dependencies included)")
        print("   3. Drag and drop XML schema files or use the file picker")
        print("")
        print("📤 Sharing:")
        print("   - Email the HTML file directly")
        print("   - Upload to any web server")
        print("   - Share via cloud storage (Dropbox, Google Drive, etc.)")
        print("   - Works on Windows, Mac, Linux, mobile devices")
        print("")
        print("🔧 Developer Features:")
        print("   - Open browser console and use window.SchemaLens.loadSampleSchema()")
        print("   - Export schemas with window.SchemaLens.exportSchema(schema, 'markdown')")
        
        return True
        
    except Exception as error:
        print(f"❌ Build failed: {error}")
        return False

if __name__ == "__main__":
    success = build_standalone()
    exit(0 if success else 1)
