<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SchemaLens Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .pass { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .fail { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>SchemaLens Test Suite</h1>
    <div id="test-results"></div>

    <script src="./src/app.js"></script>
    <script>
        // Test suite for SchemaLens
        const testResults = document.getElementById('test-results');

        function addTestResult(testName, passed, message) {
            const div = document.createElement('div');
            div.className = `test-result ${passed ? 'pass' : 'fail'}`;
            div.innerHTML = `<strong>${testName}:</strong> ${passed ? 'PASS' : 'FAIL'} - ${message}`;
            testResults.appendChild(div);
        }

        function runTests() {
            console.log('Running SchemaLens tests...');

            // Test 1: XML Parsing
            try {
                const sampleXML = `<?xml version="1.0" encoding="UTF-8"?>
                <database>
                    <table name="User">
                        <column name="id" type="int" primaryKey="true" nullable="false"/>
                        <column name="name" type="varchar(100)" nullable="false"/>
                        <column name="email" type="varchar(100)" unique="true"/>
                    </table>
                    <table name="Post">
                        <column name="id" type="int" primaryKey="true" nullable="false"/>
                        <column name="user_id" type="int" foreignKey="User.id" nullable="false"/>
                        <column name="title" type="varchar(200)" nullable="false"/>
                        <column name="content" type="text"/>
                    </table>
                </database>`;

                const schema = parseXMLSchema(sampleXML);
                
                if (schema && schema.tables && schema.tables.length === 2) {
                    addTestResult('XML Parsing', true, `Parsed ${schema.tables.length} tables successfully`);
                } else {
                    addTestResult('XML Parsing', false, 'Failed to parse tables correctly');
                }
            } catch (error) {
                addTestResult('XML Parsing', false, `Error: ${error.message}`);
            }

            // Test 2: Table Structure
            try {
                const sampleXML = `<?xml version="1.0" encoding="UTF-8"?>
                <database>
                    <table name="TestTable">
                        <column name="id" type="int" primaryKey="true" nullable="false"/>
                        <column name="name" type="varchar(100)" nullable="false"/>
                        <column name="email" type="varchar(100)" unique="true"/>
                    </table>
                </database>`;

                const schema = parseXMLSchema(sampleXML);
                const table = schema.tables[0];
                
                if (table.name === 'TestTable' && 
                    table.columns.length === 3 && 
                    table.primaryKeys.length === 1 &&
                    table.primaryKeys[0] === 'id') {
                    addTestResult('Table Structure', true, 'Table structure parsed correctly');
                } else {
                    addTestResult('Table Structure', false, 'Table structure parsing failed');
                }
            } catch (error) {
                addTestResult('Table Structure', false, `Error: ${error.message}`);
            }

            // Test 3: Foreign Key Relationships
            try {
                const sampleXML = `<?xml version="1.0" encoding="UTF-8"?>
                <database>
                    <table name="User">
                        <column name="id" type="int" primaryKey="true"/>
                    </table>
                    <table name="Post">
                        <column name="id" type="int" primaryKey="true"/>
                        <column name="user_id" type="int" foreignKey="User.id"/>
                    </table>
                </database>`;

                const schema = parseXMLSchema(sampleXML);
                
                if (schema.relationships && 
                    schema.relationships.length === 1 &&
                    schema.relationships[0].from === 'Post' &&
                    schema.relationships[0].to === 'User') {
                    addTestResult('Foreign Key Relationships', true, 'Relationships parsed correctly');
                } else {
                    addTestResult('Foreign Key Relationships', false, 'Relationship parsing failed');
                }
            } catch (error) {
                addTestResult('Foreign Key Relationships', false, `Error: ${error.message}`);
            }

            // Test 4: Mermaid Diagram Generation
            try {
                const sampleXML = `<?xml version="1.0" encoding="UTF-8"?>
                <database>
                    <table name="User">
                        <column name="id" type="int" primaryKey="true"/>
                        <column name="name" type="varchar(100)"/>
                    </table>
                </database>`;

                const schema = parseXMLSchema(sampleXML);
                const mermaidCode = generateMermaidDiagram(schema);
                
                if (mermaidCode.includes('erDiagram') && 
                    mermaidCode.includes('User') &&
                    mermaidCode.includes('int id PK')) {
                    addTestResult('Mermaid Diagram Generation', true, 'Diagram code generated correctly');
                } else {
                    addTestResult('Mermaid Diagram Generation', false, 'Diagram generation failed');
                }
            } catch (error) {
                addTestResult('Mermaid Diagram Generation', false, `Error: ${error.message}`);
            }

            // Test 5: Column Constraints
            try {
                const sampleXML = `<?xml version="1.0" encoding="UTF-8"?>
                <database>
                    <table name="TestTable">
                        <column name="id" type="int" primaryKey="true" nullable="false"/>
                        <column name="email" type="varchar(100)" unique="true" nullable="false"/>
                        <column name="name" type="varchar(100)" nullable="true" default="Unknown"/>
                    </table>
                </database>`;

                const schema = parseXMLSchema(sampleXML);
                const table = schema.tables[0];
                const idCol = table.columns.find(c => c.name === 'id');
                const emailCol = table.columns.find(c => c.name === 'email');
                const nameCol = table.columns.find(c => c.name === 'name');
                
                if (idCol.isPrimaryKey && !idCol.nullable &&
                    emailCol.isUnique && !emailCol.nullable &&
                    nameCol.nullable && nameCol.defaultValue === 'Unknown') {
                    addTestResult('Column Constraints', true, 'Constraints parsed correctly');
                } else {
                    addTestResult('Column Constraints', false, 'Constraint parsing failed');
                }
            } catch (error) {
                addTestResult('Column Constraints', false, `Error: ${error.message}`);
            }

            console.log('All tests completed!');
        }

        // Run tests when page loads
        window.addEventListener('load', runTests);
    </script>
</body>
</html>
