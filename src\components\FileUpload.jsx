import { useState } from 'react'
import { parseXMLSchema } from '../utils/xmlParser'

function FileUpload({ onSchemaLoad }) {
  const [isDragging, setIsDragging] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleFileSelect = async (file) => {
    if (!file || !file.name.toLowerCase().endsWith('.xml')) {
      alert('Please select a valid XML file')
      return
    }

    setIsLoading(true)
    try {
      const text = await file.text()
      const parsedSchema = await parseXMLSchema(text)
      onSchemaLoad(parsedSchema)
    } catch (error) {
      console.error('Error parsing XML:', error)
      alert('Error parsing XML file. Please check the file format.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDrop = (e) => {
    e.preventDefault()
    setIsDragging(false)
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const handleDragOver = (e) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleFileInput = (e) => {
    const files = Array.from(e.target.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  return (
    <div className="relative">
      <input
        type="file"
        accept=".xml"
        onChange={handleFileInput}
        className="hidden"
        id="file-upload"
        disabled={isLoading}
      />
      
      <label
        htmlFor="file-upload"
        className={`
          inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md
          transition-colors cursor-pointer
          ${isLoading 
            ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
            : 'bg-blue-600 hover:bg-blue-700 text-white'
          }
        `}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        {isLoading ? (
          <>
            <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading...
          </>
        ) : (
          <>
            <svg className="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            Upload XML Schema
          </>
        )}
      </label>

      {isDragging && (
        <div className="absolute inset-0 bg-blue-100 border-2 border-dashed border-blue-400 rounded-md flex items-center justify-center">
          <p className="text-blue-600 font-medium">Drop XML file here</p>
        </div>
      )}
    </div>
  )
}

export default FileUpload
