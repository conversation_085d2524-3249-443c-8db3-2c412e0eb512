import { useEffect, useRef, useState } from 'react'
import { generateMermaidDiagram } from '../utils/xmlParser'

function DiagramView({ schemaData }) {
  const mermaidRef = useRef(null)
  const [diagramCode, setDiagramCode] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)
  const [zoomLevel, setZoomLevel] = useState(1)

  useEffect(() => {
    // Load Mermaid from CDN if not already loaded
    if (!window.mermaid) {
      const script = document.createElement('script')
      script.src = 'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js'
      script.onload = () => {
        window.mermaid.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose',
          er: {
            diagramPadding: 20,
            layoutDirection: 'TB',
            minEntityWidth: 100,
            minEntityHeight: 75,
            entityPadding: 15,
            stroke: '#333333',
            fill: '#ECECFF',
            fontSize: 12
          }
        })
      }
      document.head.appendChild(script)
    } else {
      // Initialize Mermaid if already loaded
      window.mermaid.initialize({
        startOnLoad: false,
        theme: 'default',
        securityLevel: 'loose',
        er: {
          diagramPadding: 20,
          layoutDirection: 'TB',
          minEntityWidth: 100,
          minEntityHeight: 75,
          entityPadding: 15,
          stroke: '#333333',
          fill: '#ECECFF',
          fontSize: 12
        }
      })
    }
  }, [])

  useEffect(() => {
    if (schemaData) {
      generateDiagram()
    }
  }, [schemaData])

  const generateDiagram = async () => {
    if (!schemaData || !schemaData.tables || !window.mermaid) return

    setIsLoading(true)
    setError(null)

    try {
      const mermaidCode = generateMermaidDiagram(schemaData)
      setDiagramCode(mermaidCode)

      if (mermaidRef.current) {
        mermaidRef.current.innerHTML = ''
        const { svg } = await window.mermaid.render('mermaid-diagram', mermaidCode)
        mermaidRef.current.innerHTML = svg
      }
    } catch (err) {
      console.error('Error generating diagram:', err)
      setError('Failed to generate diagram. Please check your schema data.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.2, 3))
  }

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 0.2, 0.2))
  }

  const handleResetZoom = () => {
    setZoomLevel(1)
  }

  const handleExportPNG = async () => {
    if (!mermaidRef.current) return

    try {
      const svg = mermaidRef.current.querySelector('svg')
      if (!svg) return

      // Create a canvas to convert SVG to PNG
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      // Get SVG dimensions
      const svgRect = svg.getBoundingClientRect()
      canvas.width = svgRect.width * 2 // Higher resolution
      canvas.height = svgRect.height * 2

      // Convert SVG to data URL
      const svgData = new XMLSerializer().serializeToString(svg)
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' })
      const url = URL.createObjectURL(svgBlob)

      img.onload = () => {
        ctx.scale(2, 2) // Higher resolution
        ctx.drawImage(img, 0, 0)
        
        // Download the image
        const link = document.createElement('a')
        link.download = 'schema-diagram.png'
        link.href = canvas.toDataURL('image/png')
        link.click()
        
        URL.revokeObjectURL(url)
      }

      img.src = url
    } catch (err) {
      console.error('Error exporting PNG:', err)
      alert('Failed to export diagram as PNG')
    }
  }

  const copyDiagramCode = () => {
    navigator.clipboard.writeText(diagramCode).then(() => {
      // Could add a toast notification here
      console.log('Diagram code copied to clipboard')
    })
  }

  if (!schemaData) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="text-6xl mb-4">📊</div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            No Schema Loaded
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Upload an XML schema file to view the ER diagram
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col bg-white dark:bg-gray-800">
      {/* Toolbar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-4">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Entity Relationship Diagram
          </h2>
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {schemaData.tables.length} tables, {schemaData.relationships.length} relationships
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Zoom Controls */}
          <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              onClick={handleZoomOut}
              className="p-1 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
              title="Zoom Out"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
              </svg>
            </button>
            <span className="px-2 text-sm text-gray-600 dark:text-gray-300 min-w-[3rem] text-center">
              {Math.round(zoomLevel * 100)}%
            </span>
            <button
              onClick={handleZoomIn}
              className="p-1 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
              title="Zoom In"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            </button>
            <button
              onClick={handleResetZoom}
              className="p-1 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
              title="Reset Zoom"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>

          {/* Export Controls */}
          <button
            onClick={copyDiagramCode}
            className="px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            title="Copy Mermaid Code"
          >
            📋 Copy Code
          </button>
          <button
            onClick={handleExportPNG}
            className="px-3 py-2 text-sm bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
            title="Export as PNG"
          >
            📷 Export PNG
          </button>
        </div>
      </div>

      {/* Diagram Content */}
      <div className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-900">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <svg className="animate-spin h-8 w-8 text-blue-600 mx-auto mb-4" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <p className="text-gray-600 dark:text-gray-400">Generating diagram...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="text-6xl mb-4">⚠️</div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Diagram Generation Failed
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {error}
              </p>
              <button
                onClick={generateDiagram}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        ) : (
          <div 
            className="mermaid-container p-8 flex items-center justify-center min-h-full"
            style={{ transform: `scale(${zoomLevel})`, transformOrigin: 'center center' }}
          >
            <div ref={mermaidRef} className="max-w-full" />
          </div>
        )}
      </div>
    </div>
  )
}

export default DiagramView
