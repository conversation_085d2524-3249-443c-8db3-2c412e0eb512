# SchemaLens PowerShell Launcher
$AppPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$SchemaLensPath = Join-Path $AppPath "App\SchemaLens"

Set-Location $SchemaLensPath

# Try to start with Python
try {
    $pythonVersion = python --version 2>$null
    if ($pythonVersion) {
        Write-Host "Starting SchemaLens with Python..."
        Start-Process python -ArgumentList "-m", "http.server", "8080" -WindowStyle Hidden
        Start-Sleep 2
        Start-Process "http://localhost:8080"
    } else {
        throw "Python not found"
    }
} catch {
    # Fallback to HTML
    Write-Host "Opening SchemaLens HTML file..."
    Start-Process "SchemaLens-Complete.html"
}

Write-Host "SchemaLens started. Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
