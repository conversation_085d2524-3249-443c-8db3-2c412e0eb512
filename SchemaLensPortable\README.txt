# SchemaLens Portable

## For Restricted Corporate Environments

This version is designed to work in corporate environments with strict security policies.

### Running Options (in order of preference):

1. **PowerShell Launcher** (Recommended)
   - Right-click `SchemaLensPortable.ps1`
   - Select "Run with PowerShell"
   - May need to allow execution policy

2. **Batch Launcher**
   - Double-click `SchemaLensPortable.bat`
   - Should work in most environments

3. **Direct HTML**
   - Navigate to `App/SchemaLens/`
   - Open `SchemaLens-Complete.html` in browser
   - Works even with strictest policies

4. **Manual Python**
   - Open Command Prompt in `App/SchemaLens/`
   - Run: `python -m http.server 8080`
   - Open browser to `http://localhost:8080`

### Bypassing Common Restrictions:

- **Executable Blocked**: Use HTML version
- **PowerShell Disabled**: Use batch file
- **Batch Files Blocked**: Open HTML directly
- **Python Blocked**: HTML version works standalone
- **Internet Restricted**: Everything works offline

### Features:
- No installation required
- No admin rights needed
- Works from USB drive
- Completely portable
- Multiple fallback options

### Troubleshooting:
- If nothing works, try copying just the HTML file to desktop
- Some environments allow running from specific folders (Documents, Desktop)
- Try renaming .bat to .cmd or .txt then rename back
