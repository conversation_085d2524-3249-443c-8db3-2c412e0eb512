#!/usr/bin/env node

/**
 * Build Script for SchemaLens Standalone HTML
 * Creates a single self-contained HTML file with all dependencies
 */

const fs = require('fs');
const path = require('path');

console.log('🔨 Building SchemaLens Standalone Edition...');

try {
    // Read the template HTML file
    const templatePath = path.join(__dirname, 'SchemaLens-Standalone.html');
    let htmlContent = fs.readFileSync(templatePath, 'utf8');
    
    // Read the main application JavaScript
    const appJsPath = path.join(__dirname, 'src', 'app.js');
    const appJsContent = fs.readFileSync(appJsPath, 'utf8');
    
    // Read the sample schema for embedding
    const sampleSchemaPath = path.join(__dirname, 'sample-schema.xml');
    const sampleSchemaContent = fs.readFileSync(sampleSchemaPath, 'utf8');
    
    // Escape the sample schema content for embedding in JavaScript
    const escapedSampleSchema = sampleSchemaContent
        .replace(/\\/g, '\\\\')
        .replace(/`/g, '\\`')
        .replace(/\$/g, '\\$');
    
    // Create the complete JavaScript content
    const completeJsContent = `
        // SchemaLens Standalone Edition - Complete Application
        // Generated on: ${new Date().toISOString()}
        
        // Embedded sample schema
        const EMBEDDED_SAMPLE_SCHEMA = \`${escapedSampleSchema}\`;
        
        // Main application code
        ${appJsContent}
        
        // Auto-load sample schema on startup
        document.addEventListener('DOMContentLoaded', function() {
            // Add a button to load the embedded sample
            setTimeout(() => {
                const app = document.querySelector('#root');
                if (app && app.innerHTML.includes('Welcome to SchemaLens')) {
                    const loadSampleBtn = document.createElement('button');
                    loadSampleBtn.className = 'mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors';
                    loadSampleBtn.textContent = '📊 Load Sample Schema';
                    loadSampleBtn.onclick = () => {
                        try {
                            const parsedSchema = parseXMLSchema(EMBEDDED_SAMPLE_SCHEMA);
                            // This would need to be connected to the React app state
                            console.log('Sample schema loaded:', parsedSchema);
                            alert('Sample schema loaded! (This is a demo - full integration needed)');
                        } catch (error) {
                            console.error('Error loading sample:', error);
                            alert('Error loading sample schema');
                        }
                    };
                    
                    const welcomeDiv = document.querySelector('#root > div > div > div');
                    if (welcomeDiv) {
                        welcomeDiv.appendChild(loadSampleBtn);
                    }
                }
            }, 1000);
        });
    `;
    
    // Replace the placeholder JavaScript with the complete content
    htmlContent = htmlContent.replace(
        /<!-- Application JavaScript -->\s*<script>[\s\S]*?<\/script>/,
        `<!-- Application JavaScript -->
    <script>
${completeJsContent}
    </script>`
    );
    
    // Add build information
    const buildInfo = `
    <!-- Build Information -->
    <!-- Generated: ${new Date().toISOString()} -->
    <!-- Version: 1.0.0 -->
    <!-- Size: ~${Math.round(htmlContent.length / 1024)}KB -->
    `;
    
    htmlContent = htmlContent.replace('</head>', `${buildInfo}</head>`);
    
    // Write the complete standalone file
    const outputPath = path.join(__dirname, 'SchemaLens-Complete.html');
    fs.writeFileSync(outputPath, htmlContent, 'utf8');
    
    // Get file size
    const stats = fs.statSync(outputPath);
    const fileSizeKB = Math.round(stats.size / 1024);
    
    console.log('✅ Build completed successfully!');
    console.log(`📁 Output: ${outputPath}`);
    console.log(`📊 Size: ${fileSizeKB}KB`);
    console.log('');
    console.log('🚀 Usage:');
    console.log('   1. Open SchemaLens-Complete.html in any modern web browser');
    console.log('   2. No internet connection required (all dependencies included)');
    console.log('   3. Drag and drop XML schema files or use the file picker');
    console.log('');
    console.log('📤 Sharing:');
    console.log('   - Email the HTML file directly');
    console.log('   - Upload to any web server');
    console.log('   - Share via cloud storage (Dropbox, Google Drive, etc.)');
    console.log('   - Works on Windows, Mac, Linux, mobile devices');
    
} catch (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
}
