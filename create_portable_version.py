#!/usr/bin/env python3
"""
Create PortableApps-compatible version of SchemaLens
This bypasses many corporate restrictions
"""

import os
import shutil
import zipfile
from pathlib import Path

def create_portable_app():
    """Create a portable app structure"""
    print("📦 Creating PortableApps-compatible SchemaLens...")
    
    app_dir = "SchemaLensPortable"
    
    # Clean and create directory structure
    if os.path.exists(app_dir):
        shutil.rmtree(app_dir)
    
    # Create PortableApps structure
    dirs = [
        f"{app_dir}/App/SchemaLens",
        f"{app_dir}/App/AppInfo",
        f"{app_dir}/Data",
        f"{app_dir}/Other/Source"
    ]

    for dir_path in dirs:
        os.makedirs(dir_path, exist_ok=True)
    
    # Copy application files
    app_files = [
        "index.html",
        "src/app.js",
        "sample-schema.xml",
        "manifest.json",
        "sw.js",
        "SchemaLens-Complete.html"
    ]
    
    for file_path in app_files:
        if os.path.exists(file_path):
            if os.path.isfile(file_path):
                dest_dir = f"{app_dir}/App/SchemaLens"
                os.makedirs(dest_dir, exist_ok=True)
                shutil.copy2(file_path, dest_dir)
            else:
                dest_dir = f"{app_dir}/App/SchemaLens/{os.path.basename(file_path)}"
                shutil.copytree(file_path, dest_dir, dirs_exist_ok=True)
    
    # Create launcher script (BAT file that looks less suspicious)
    launcher_content = '''@echo off
setlocal
cd /d "%~dp0App\\SchemaLens"

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Starting SchemaLens with Python...
    start "" python -m http.server 8080
    timeout /t 2 /nobreak >nul
    start "" "http://localhost:8080"
) else (
    REM Fallback to opening HTML directly
    echo Opening SchemaLens in browser...
    start "" "SchemaLens-Complete.html"
)
'''
    
    with open(f"{app_dir}/SchemaLensPortable.bat", "w") as f:
        f.write(launcher_content)
    
    # Create PowerShell launcher (often less restricted)
    ps_launcher = '''# SchemaLens PowerShell Launcher
$AppPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$SchemaLensPath = Join-Path $AppPath "App\\SchemaLens"

Set-Location $SchemaLensPath

# Try to start with Python
try {
    $pythonVersion = python --version 2>$null
    if ($pythonVersion) {
        Write-Host "Starting SchemaLens with Python..."
        Start-Process python -ArgumentList "-m", "http.server", "8080" -WindowStyle Hidden
        Start-Sleep 2
        Start-Process "http://localhost:8080"
    } else {
        throw "Python not found"
    }
} catch {
    # Fallback to HTML
    Write-Host "Opening SchemaLens HTML file..."
    Start-Process "SchemaLens-Complete.html"
}

Write-Host "SchemaLens started. Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
'''
    
    with open(f"{app_dir}/SchemaLensPortable.ps1", "w", encoding='utf-8') as f:
        f.write(ps_launcher)
    
    # Create app info file
    app_info = '''[Format]
Type=PortableApps.comFormat
Version=3.0

[Details]
Name=SchemaLens Portable
AppID=SchemaLensPortable
Publisher=SchemaLens
Homepage=https://github.com/schemalens
Category=Development
Description=Database Schema Explorer and Analyzer
Language=English

[License]
Shareable=true
OpenSource=true
Freeware=true
CommercialUse=true

[Version]
PackageVersion=*******
DisplayVersion=1.0.0

[Control]
Icons=1
Start=SchemaLensPortable.bat
'''
    
    with open(f"{app_dir}/App/AppInfo/appinfo.ini", "w") as f:
        f.write(app_info)
    
    # Create README
    readme_content = '''# SchemaLens Portable

## For Restricted Corporate Environments

This version is designed to work in corporate environments with strict security policies.

### Running Options (in order of preference):

1. **PowerShell Launcher** (Recommended)
   - Right-click `SchemaLensPortable.ps1`
   - Select "Run with PowerShell"
   - May need to allow execution policy

2. **Batch Launcher**
   - Double-click `SchemaLensPortable.bat`
   - Should work in most environments

3. **Direct HTML**
   - Navigate to `App/SchemaLens/`
   - Open `SchemaLens-Complete.html` in browser
   - Works even with strictest policies

4. **Manual Python**
   - Open Command Prompt in `App/SchemaLens/`
   - Run: `python -m http.server 8080`
   - Open browser to `http://localhost:8080`

### Bypassing Common Restrictions:

- **Executable Blocked**: Use HTML version
- **PowerShell Disabled**: Use batch file
- **Batch Files Blocked**: Open HTML directly
- **Python Blocked**: HTML version works standalone
- **Internet Restricted**: Everything works offline

### Features:
- No installation required
- No admin rights needed
- Works from USB drive
- Completely portable
- Multiple fallback options

### Troubleshooting:
- If nothing works, try copying just the HTML file to desktop
- Some environments allow running from specific folders (Documents, Desktop)
- Try renaming .bat to .cmd or .txt then rename back
'''
    
    with open(f"{app_dir}/README.txt", "w", encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ Portable app created: {app_dir}/")
    return app_dir

def create_stealth_version():
    """Create a version that looks like a document"""
    print("🥷 Creating stealth version...")
    
    stealth_dir = "DatabaseDocumentation"
    os.makedirs(stealth_dir, exist_ok=True)
    
    # Copy HTML file with innocent name
    if os.path.exists("SchemaLens-Complete.html"):
        shutil.copy2("SchemaLens-Complete.html", f"{stealth_dir}/Database_Schema_Documentation.html")
    
    # Create innocent-looking batch file
    stealth_launcher = '''@echo off
title Database Documentation Viewer
echo Loading database documentation...
start "" "Database_Schema_Documentation.html"
'''
    
    with open(f"{stealth_dir}/View_Documentation.bat", "w") as f:
        f.write(stealth_launcher)
    
    # Create Word document launcher (if Word is available)
    word_launcher = '''Set objShell = CreateObject("WScript.Shell")
objShell.Run "Database_Schema_Documentation.html"
'''
    
    with open(f"{stealth_dir}/Open_Database_Docs.vbs", "w") as f:
        f.write(word_launcher)
    
    print(f"✅ Stealth version created: {stealth_dir}/")
    return stealth_dir

def create_usb_version():
    """Create USB-friendly version"""
    print("💾 Creating USB version...")
    
    usb_dir = "SchemaLens_USB"
    os.makedirs(usb_dir, exist_ok=True)
    
    # Copy all files
    files_to_copy = [
        "SchemaLens-Complete.html",
        "sample-schema.xml"
    ]
    
    for file_path in files_to_copy:
        if os.path.exists(file_path):
            shutil.copy2(file_path, usb_dir)
    
    # Create autorun.inf (for USB autoplay)
    autorun_content = '''[autorun]
open=SchemaLens-Complete.html
icon=schemalens.ico
label=SchemaLens Database Tool
'''
    
    with open(f"{usb_dir}/autorun.inf", "w") as f:
        f.write(autorun_content)
    
    # Create simple launcher
    usb_launcher = '''@echo off
echo SchemaLens - Database Schema Explorer
echo.
echo Opening in your default browser...
start "" "SchemaLens-Complete.html"
'''
    
    with open(f"{usb_dir}/Start.bat", "w") as f:
        f.write(usb_launcher)
    
    print(f"✅ USB version created: {usb_dir}/")
    return usb_dir

def main():
    """Create all portable versions"""
    print("🔓 Creating Corporate-Friendly SchemaLens Versions")
    print("=" * 60)
    
    versions = []
    
    # Create different versions
    versions.append(create_portable_app())
    versions.append(create_stealth_version())
    versions.append(create_usb_version())
    
    # Create ZIP packages
    print("\n📦 Creating ZIP packages...")
    for version_dir in versions:
        if os.path.exists(version_dir):
            zip_name = f"{version_dir}.zip"
            with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(version_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_path = os.path.relpath(file_path, version_dir)
                        zipf.write(file_path, arc_path)
            print(f"✅ Created: {zip_name}")
    
    print("\n🎉 All versions created!")
    print("\n📋 Corporate Environment Solutions:")
    print("1. SchemaLensPortable/ - PortableApps format")
    print("2. DatabaseDocumentation/ - Stealth version")
    print("3. SchemaLens_USB/ - USB-friendly version")
    print("\n💡 Try them in order until one works in your environment!")

if __name__ == "__main__":
    main()
