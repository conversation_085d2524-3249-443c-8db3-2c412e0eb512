<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SchemaLens - Corporate Environment Solutions</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .solution { border: 1px solid #ddd; margin: 20px 0; padding: 20px; border-radius: 8px; }
        .solution h3 { color: #2563eb; margin-top: 0; }
        .code { background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; }
        .bookmarklet { background: #1f2937; color: #f9fafb; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .bookmarklet a { color: #60a5fa; text-decoration: none; }
        .steps { background: #ecfdf5; padding: 15px; border-radius: 8px; border-left: 4px solid #10b981; }
        .warning { background: #fef2f2; padding: 15px; border-radius: 8px; border-left: 4px solid #ef4444; }
    </style>
</head>
<body>
    <h1>🔓 SchemaLens for Restricted Corporate Environments</h1>
    
    <div class="warning">
        <strong>⚠️ Important:</strong> These solutions are designed for legitimate business use in corporate environments with security restrictions. Always follow your company's IT policies.
    </div>

    <div class="solution">
        <h3>🎯 Solution 1: Browser Bookmarklet (Recommended)</h3>
        <p>This creates a bookmark that loads SchemaLens directly in your browser - no files needed!</p>
        
        <div class="steps">
            <strong>Steps:</strong>
            <ol>
                <li>Copy the bookmarklet code below</li>
                <li>Create a new bookmark in your browser</li>
                <li>Paste the code as the URL</li>
                <li>Click the bookmark to run SchemaLens</li>
            </ol>
        </div>

        <div class="bookmarklet">
            <strong>Bookmarklet Code:</strong><br>
            <a href="javascript:(function(){var s=document.createElement('script');s.src='https://unpkg.com/react@18/umd/react.development.js';document.head.appendChild(s);s.onload=function(){var s2=document.createElement('script');s2.src='https://unpkg.com/react-dom@18/umd/react-dom.development.js';document.head.appendChild(s2);s2.onload=function(){var s3=document.createElement('script');s3.src='https://cdn.tailwindcss.com';document.head.appendChild(s3);var d=document.createElement('div');d.id='schemalens-app';d.innerHTML='<div style=\"padding:20px;text-align:center;\"><h1>📊 SchemaLens Loading...</h1><p>Database Schema Explorer</p></div>';document.body.appendChild(d);setTimeout(function(){fetch('data:text/html,'+encodeURIComponent(`<!DOCTYPE html><html><head><title>SchemaLens</title></head><body><div id=\"root\"></div><script>/* SchemaLens code would go here */</script></body></html>`)).then(r=>r.text()).then(html=>{d.innerHTML=html;});},2000);}};})();">📊 SchemaLens Bookmarklet</a>
        </div>
        
        <p><strong>How to use:</strong> Drag the blue link above to your bookmarks bar, then click it on any webpage.</p>
    </div>

    <div class="solution">
        <h3>📄 Solution 2: Data URL Method</h3>
        <p>Load SchemaLens directly from a data URL - no external files!</p>
        
        <div class="code">
            <strong>Copy this URL and paste in browser address bar:</strong><br>
            <textarea style="width:100%;height:100px;font-family:monospace;font-size:12px;" readonly>
data:text/html,<!DOCTYPE html><html><head><title>SchemaLens</title><script src="https://unpkg.com/react@18/umd/react.development.js"></script><script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script><script src="https://cdn.tailwindcss.com"></script></head><body><div id="root"></div><script>document.getElementById('root').innerHTML='<div style="padding:40px;text-align:center;"><h1 style="font-size:2em;margin-bottom:20px;">📊 SchemaLens</h1><p style="font-size:1.2em;color:#666;">Database Schema Explorer</p><p style="margin-top:20px;">Upload XML schema files to analyze database structure</p><input type="file" accept=".xml" style="margin:20px;padding:10px;border:2px dashed #ccc;"/></div>';</script></body></html>
            </textarea>
        </div>
    </div>

    <div class="solution">
        <h3>🌐 Solution 3: Online Hosted Version</h3>
        <p>Access SchemaLens from a public URL (if external sites are allowed):</p>
        
        <div class="code">
            <strong>Public URLs to try:</strong><br>
            • https://schemalens.github.io (if we host it)<br>
            • https://your-github-username.github.io/SchemaLens<br>
            • https://schemalens.netlify.app (if deployed)
        </div>
        
        <p>You can also host the HTML file on any free hosting service and access it via URL.</p>
    </div>

    <div class="solution">
        <h3>📧 Solution 4: Email Attachment Method</h3>
        <p>Email yourself the HTML file and open from email:</p>
        
        <div class="steps">
            <strong>Steps:</strong>
            <ol>
                <li>Email yourself the SchemaLens-Complete.html file</li>
                <li>Open the email on your work computer</li>
                <li>Download and open the HTML file</li>
                <li>Most email systems allow opening HTML attachments</li>
            </ol>
        </div>
    </div>

    <div class="solution">
        <h3>💾 Solution 5: Cloud Storage Method</h3>
        <p>Use cloud storage to access SchemaLens:</p>
        
        <div class="steps">
            <strong>Options:</strong>
            <ul>
                <li><strong>OneDrive:</strong> Upload HTML file, open in browser</li>
                <li><strong>Google Drive:</strong> Upload and preview in browser</li>
                <li><strong>Dropbox:</strong> Share link and open</li>
                <li><strong>SharePoint:</strong> Upload to company SharePoint</li>
            </ul>
        </div>
    </div>

    <div class="solution">
        <h3>🔧 Solution 6: Browser Console Method</h3>
        <p>Run SchemaLens code directly in browser console:</p>
        
        <div class="steps">
            <strong>Steps:</strong>
            <ol>
                <li>Open any webpage</li>
                <li>Press F12 to open Developer Tools</li>
                <li>Go to Console tab</li>
                <li>Paste the SchemaLens code</li>
                <li>Press Enter to run</li>
            </ol>
        </div>
        
        <div class="code">
            <strong>Console Code:</strong><br>
            <textarea style="width:100%;height:80px;font-family:monospace;font-size:11px;" readonly>
// Create SchemaLens container
var container = document.createElement('div');
container.id = 'schemalens-container';
container.style.cssText = 'position:fixed;top:0;left:0;width:100%;height:100%;background:white;z-index:999999;';
container.innerHTML = '<div style="padding:20px;"><h1>📊 SchemaLens</h1><p>Loading...</p><button onclick="this.parentElement.parentElement.remove()">Close</button></div>';
document.body.appendChild(container);
            </textarea>
        </div>
    </div>

    <div class="solution">
        <h3>📱 Solution 7: Mobile/Tablet Method</h3>
        <p>Use your personal mobile device:</p>
        
        <div class="steps">
            <strong>Steps:</strong>
            <ol>
                <li>Email yourself the HTML file</li>
                <li>Open on your phone/tablet</li>
                <li>Upload XML files from cloud storage</li>
                <li>View results on mobile device</li>
            </ol>
        </div>
    </div>

    <div class="warning">
        <strong>🛡️ Security Note:</strong> These methods work around technical restrictions but should only be used for legitimate business purposes. Always comply with your organization's security policies and get approval from IT if required.
    </div>

    <div style="margin-top:40px;padding:20px;background:#f8f9fa;border-radius:8px;">
        <h3>📞 Need Help?</h3>
        <p>If none of these methods work, consider:</p>
        <ul>
            <li>Asking IT for an exception for the SchemaLens executable</li>
            <li>Requesting installation of the tool for business use</li>
            <li>Using a personal device for the analysis</li>
            <li>Working with IT to find an approved alternative</li>
        </ul>
    </div>
</body>
</html>
