<?xml version="1.0" encoding="UTF-8"?>
<database>
  <table name="Customer" description="Customer information and account details">
    <column name="CustomerID" type="int" primaryKey="true" nullable="false" description="Unique identifier for customer"/>
    <column name="FirstName" type="varchar(50)" nullable="false" description="Customer's first name"/>
    <column name="LastName" type="varchar(50)" nullable="false" description="Customer's last name"/>
    <column name="Email" type="varchar(100)" unique="true" nullable="false" description="Customer's email address"/>
    <column name="Phone" type="varchar(20)" description="Customer's phone number"/>
    <column name="DateCreated" type="datetime" nullable="false" default="CURRENT_TIMESTAMP" description="When the customer record was created"/>
    <column name="IsActive" type="boolean" nullable="false" default="true" description="Whether the customer account is active"/>
    <column name="CustomerType" type="varchar(20)" nullable="false" default="Regular" description="Type of customer (Regular, Premium, VIP)"/>
    <column name="CreditLimit" type="decimal(10,2)" default="1000.00" description="Customer's credit limit"/>
  </table>

  <table name="Category" description="Product categories with hierarchical structure">
    <column name="CategoryID" type="int" primaryKey="true" nullable="false" description="Unique identifier for category"/>
    <column name="CategoryName" type="varchar(100)" nullable="false" unique="true" description="Name of the product category"/>
    <column name="Description" type="text" description="Category description"/>
    <column name="ParentCategoryID" type="int" foreignKey="Category.CategoryID" description="Reference to parent category for hierarchical structure"/>
    <column name="SortOrder" type="int" default="0" description="Display order for categories"/>
    <column name="IsActive" type="boolean" nullable="false" default="true" description="Whether the category is active"/>
  </table>

  <table name="Product" description="Product catalog with pricing and inventory">
    <column name="ProductID" type="int" primaryKey="true" nullable="false" description="Unique identifier for product"/>
    <column name="ProductName" type="varchar(200)" nullable="false" description="Name of the product"/>
    <column name="CategoryID" type="int" foreignKey="Category.CategoryID" nullable="false" description="Reference to product category"/>
    <column name="Price" type="decimal(10,2)" nullable="false" description="Product price"/>
    <column name="StockQuantity" type="int" nullable="false" default="0" description="Current stock quantity"/>
    <column name="SKU" type="varchar(50)" unique="true" nullable="false" description="Stock Keeping Unit"/>
    <column name="Description" type="text" description="Product description"/>
    <column name="IsActive" type="boolean" nullable="false" default="true" description="Whether the product is active"/>
    <column name="DateCreated" type="datetime" nullable="false" default="CURRENT_TIMESTAMP" description="When the product was created"/>
    <column name="Weight" type="decimal(8,2)" description="Product weight in kg"/>
    <column name="Dimensions" type="varchar(50)" description="Product dimensions (LxWxH)"/>
  </table>

  <table name="Order" description="Customer orders and order management">
    <column name="OrderID" type="int" primaryKey="true" nullable="false" description="Unique identifier for order"/>
    <column name="CustomerID" type="int" foreignKey="Customer.CustomerID" nullable="false" description="Reference to customer who placed the order"/>
    <column name="OrderDate" type="datetime" nullable="false" default="CURRENT_TIMESTAMP" description="When the order was placed"/>
    <column name="TotalAmount" type="decimal(10,2)" nullable="false" description="Total order amount"/>
    <column name="Status" type="varchar(20)" nullable="false" default="Pending" description="Order status (Pending, Processing, Shipped, Delivered, Cancelled)"/>
    <column name="ShippingAddress" type="text" nullable="false" description="Shipping address for the order"/>
    <column name="PaymentMethod" type="varchar(50)" description="Payment method used"/>
    <column name="ShippingCost" type="decimal(8,2)" default="0.00" description="Shipping cost for the order"/>
    <column name="TaxAmount" type="decimal(8,2)" default="0.00" description="Tax amount for the order"/>
  </table>

  <table name="OrderItem" description="Individual items within an order">
    <column name="OrderItemID" type="int" primaryKey="true" nullable="false" description="Unique identifier for order item"/>
    <column name="OrderID" type="int" foreignKey="Order.OrderID" nullable="false" description="Reference to the order"/>
    <column name="ProductID" type="int" foreignKey="Product.ProductID" nullable="false" description="Reference to the product"/>
    <column name="Quantity" type="int" nullable="false" description="Quantity of the product ordered"/>
    <column name="UnitPrice" type="decimal(10,2)" nullable="false" description="Price per unit at the time of order"/>
    <column name="TotalPrice" type="decimal(10,2)" nullable="false" description="Total price for this line item"/>
    <column name="DiscountAmount" type="decimal(8,2)" default="0.00" description="Discount applied to this item"/>
  </table>

  <table name="Review" description="Product reviews and ratings from customers">
    <column name="ReviewID" type="int" primaryKey="true" nullable="false" description="Unique identifier for review"/>
    <column name="ProductID" type="int" foreignKey="Product.ProductID" nullable="false" description="Reference to the reviewed product"/>
    <column name="CustomerID" type="int" foreignKey="Customer.CustomerID" nullable="false" description="Reference to customer who wrote the review"/>
    <column name="Rating" type="int" nullable="false" description="Rating from 1 to 5"/>
    <column name="ReviewText" type="text" description="Review content"/>
    <column name="ReviewDate" type="datetime" nullable="false" default="CURRENT_TIMESTAMP" description="When the review was written"/>
    <column name="IsVerified" type="boolean" nullable="false" default="false" description="Whether the review is verified"/>
    <column name="HelpfulVotes" type="int" default="0" description="Number of helpful votes for this review"/>
  </table>

  <table name="Supplier" description="Supplier information and contact details">
    <column name="SupplierID" type="int" primaryKey="true" nullable="false" description="Unique identifier for supplier"/>
    <column name="SupplierName" type="varchar(200)" nullable="false" description="Name of the supplier"/>
    <column name="ContactEmail" type="varchar(100)" unique="true" nullable="false" description="Supplier contact email"/>
    <column name="ContactPhone" type="varchar(20)" description="Supplier contact phone"/>
    <column name="Address" type="text" description="Supplier address"/>
    <column name="IsActive" type="boolean" nullable="false" default="true" description="Whether the supplier is active"/>
    <column name="Rating" type="decimal(3,2)" description="Supplier rating (1.00 to 5.00)"/>
    <column name="PaymentTerms" type="varchar(50)" description="Payment terms with supplier"/>
  </table>

  <table name="ProductSupplier" description="Many-to-many relationship between products and suppliers">
    <column name="ProductSupplierID" type="int" primaryKey="true" nullable="false" description="Unique identifier for product-supplier relationship"/>
    <column name="ProductID" type="int" foreignKey="Product.ProductID" nullable="false" description="Reference to the product"/>
    <column name="SupplierID" type="int" foreignKey="Supplier.SupplierID" nullable="false" description="Reference to the supplier"/>
    <column name="SupplierPrice" type="decimal(10,2)" nullable="false" description="Price from this supplier"/>
    <column name="LeadTimeDays" type="int" description="Lead time in days"/>
    <column name="IsPreferred" type="boolean" nullable="false" default="false" description="Whether this is the preferred supplier for this product"/>
    <column name="MinOrderQuantity" type="int" default="1" description="Minimum order quantity"/>
    <column name="LastOrderDate" type="datetime" description="Date of last order from this supplier"/>
  </table>

  <table name="Inventory" description="Inventory tracking and stock movements">
    <column name="InventoryID" type="int" primaryKey="true" nullable="false" description="Unique identifier for inventory record"/>
    <column name="ProductID" type="int" foreignKey="Product.ProductID" nullable="false" description="Reference to the product"/>
    <column name="MovementType" type="varchar(20)" nullable="false" description="Type of movement (IN, OUT, ADJUSTMENT)"/>
    <column name="Quantity" type="int" nullable="false" description="Quantity moved (positive for IN, negative for OUT)"/>
    <column name="MovementDate" type="datetime" nullable="false" default="CURRENT_TIMESTAMP" description="When the movement occurred"/>
    <column name="Reference" type="varchar(100)" description="Reference to order, purchase, or adjustment"/>
    <column name="Notes" type="text" description="Additional notes about the movement"/>
  </table>
</database>
