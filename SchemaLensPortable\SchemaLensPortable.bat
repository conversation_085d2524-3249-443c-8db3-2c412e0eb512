@echo off
setlocal
cd /d "%~dp0App\SchemaLens"

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Starting SchemaLens with Python...
    start "" python -m http.server 8080
    timeout /t 2 /nobreak >nul
    start "" "http://localhost:8080"
) else (
    REM Fallback to opening HTML directly
    echo Opening SchemaLens in browser...
    start "" "SchemaLens-Complete.html"
)
