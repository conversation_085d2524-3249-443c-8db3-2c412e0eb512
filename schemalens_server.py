#!/usr/bin/env python3
"""
SchemaLens Desktop Server
Main entry point for the executable version
"""

import os
import sys
import http.server
import socketserver
import webbrowser
import threading
import time
import tkinter as tk
from tkinter import messagebox, ttk
import tempfile
import shutil
from pathlib import Path

class SchemaLensServer:
    def __init__(self):
        self.port = 8080
        self.server = None
        self.server_thread = None
        self.temp_dir = None
        self.setup_temp_directory()
        
    def setup_temp_directory(self):
        """Create temporary directory with web files"""
        self.temp_dir = tempfile.mkdtemp(prefix="schemalens_")
        
        # Get the directory where the executable is located
        if getattr(sys, 'frozen', False):
            # Running as executable
            bundle_dir = sys._MEIPASS
        else:
            # Running as script
            bundle_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Copy web files to temp directory
        web_files = ['index.html', 'manifest.json', 'sw.js', 'sample-schema.xml']
        
        for file in web_files:
            src_path = os.path.join(bundle_dir, file)
            if os.path.exists(src_path):
                shutil.copy2(src_path, self.temp_dir)
        
        # Copy src directory
        src_dir = os.path.join(bundle_dir, 'src')
        if os.path.exists(src_dir):
            shutil.copytree(src_dir, os.path.join(self.temp_dir, 'src'), dirs_exist_ok=True)
    
    def find_free_port(self):
        """Find a free port to use"""
        import socket
        for port in range(8080, 8090):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    self.port = port
                    return port
            except OSError:
                continue
        return 8080
    
    def start_server(self):
        """Start the HTTP server"""
        self.find_free_port()
        os.chdir(self.temp_dir)
        
        class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
            def end_headers(self):
                self.send_header('Service-Worker-Allowed', '/')
                self.send_header('Cache-Control', 'no-cache')
                super().end_headers()
            
            def log_message(self, format, *args):
                # Suppress server logs
                pass
        
        try:
            self.server = socketserver.TCPServer(("localhost", self.port), CustomHTTPRequestHandler)
            self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
            self.server_thread.start()
            return True
        except Exception as e:
            print(f"Failed to start server: {e}")
            return False
    
    def stop_server(self):
        """Stop the HTTP server"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
        
        # Clean up temp directory
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
            except:
                pass
    
    def open_browser(self):
        """Open SchemaLens in the default browser"""
        url = f"http://localhost:{self.port}"
        try:
            webbrowser.open(url)
            return True
        except Exception as e:
            print(f"Failed to open browser: {e}")
            return False

class SchemaLensGUI:
    def __init__(self):
        self.server = SchemaLensServer()
        self.root = tk.Tk()
        self.setup_gui()
        
    def setup_gui(self):
        """Setup the GUI interface"""
        self.root.title("SchemaLens - Database Schema Explorer")
        self.root.geometry("400x300")
        self.root.resizable(False, False)
        
        # Set icon (using emoji as fallback)
        try:
            self.root.iconbitmap(default="schemalens.ico")
        except:
            pass
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="📊 SchemaLens", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        subtitle_label = ttk.Label(main_frame, text="Database Schema Explorer", font=("Arial", 10))
        subtitle_label.grid(row=1, column=0, columnspan=2, pady=(0, 20))
        
        # Status
        self.status_var = tk.StringVar(value="Ready to start")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.grid(row=2, column=0, columnspan=2, pady=(0, 10))
        
        # Buttons
        self.start_button = ttk.Button(main_frame, text="🚀 Start SchemaLens", command=self.start_schemalens)
        self.start_button.grid(row=3, column=0, columnspan=2, pady=5, sticky=(tk.W, tk.E))
        
        self.browser_button = ttk.Button(main_frame, text="🌐 Open in Browser", command=self.open_browser, state="disabled")
        self.browser_button.grid(row=4, column=0, columnspan=2, pady=5, sticky=(tk.W, tk.E))
        
        self.stop_button = ttk.Button(main_frame, text="⏹️ Stop Server", command=self.stop_schemalens, state="disabled")
        self.stop_button.grid(row=5, column=0, columnspan=2, pady=5, sticky=(tk.W, tk.E))
        
        # Info
        info_text = "SchemaLens will start a local web server\nand open in your default browser."
        info_label = ttk.Label(main_frame, text=info_text, font=("Arial", 9), foreground="gray")
        info_label.grid(row=6, column=0, columnspan=2, pady=(20, 0))
        
        # URL display
        self.url_var = tk.StringVar()
        url_label = ttk.Label(main_frame, textvariable=self.url_var, font=("Arial", 9), foreground="blue")
        url_label.grid(row=7, column=0, columnspan=2, pady=(10, 0))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        
        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def start_schemalens(self):
        """Start SchemaLens server"""
        self.status_var.set("Starting server...")
        self.start_button.config(state="disabled")
        
        if self.server.start_server():
            self.status_var.set(f"Server running on port {self.server.port}")
            self.url_var.set(f"http://localhost:{self.server.port}")
            self.browser_button.config(state="normal")
            self.stop_button.config(state="normal")
            
            # Auto-open browser
            time.sleep(1)  # Give server time to start
            self.server.open_browser()
        else:
            self.status_var.set("Failed to start server")
            self.start_button.config(state="normal")
            messagebox.showerror("Error", "Failed to start SchemaLens server")
    
    def open_browser(self):
        """Open browser manually"""
        if not self.server.open_browser():
            messagebox.showerror("Error", "Failed to open browser")
    
    def stop_schemalens(self):
        """Stop SchemaLens server"""
        self.server.stop_server()
        self.status_var.set("Server stopped")
        self.url_var.set("")
        self.start_button.config(state="normal")
        self.browser_button.config(state="disabled")
        self.stop_button.config(state="disabled")
    
    def on_closing(self):
        """Handle application closing"""
        self.server.stop_server()
        self.root.destroy()
    
    def run(self):
        """Run the GUI application"""
        self.root.mainloop()

def main():
    """Main entry point"""
    try:
        # Check if running in GUI mode or console mode
        if len(sys.argv) > 1 and sys.argv[1] == '--console':
            # Console mode
            server = SchemaLensServer()
            print("🚀 Starting SchemaLens...")
            
            if server.start_server():
                print(f"✅ Server running at http://localhost:{server.port}")
                print("🌐 Opening in browser...")
                server.open_browser()
                print("⏹️  Press Ctrl+C to stop")
                
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\n🛑 Stopping server...")
                    server.stop_server()
                    print("✅ Server stopped")
            else:
                print("❌ Failed to start server")
        else:
            # GUI mode (default)
            app = SchemaLensGUI()
            app.run()
    
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
