# SchemaLens

A cross-platform desktop application for exploring XML-based SQL schemas with interactive diagrams and detailed table views.

## 🚀 Features

- **📁 XML Schema Upload**: Load XML-based SQL schema files via drag-and-drop or file selection
- **🔍 Global Search**: Search across all tables and columns with real-time suggestions
- **📊 ER Diagram Generation**: Auto-generate interactive Entity Relationship diagrams using Mermaid.js
- **🗂️ Table Explorer**: Browse tables with detailed column information, constraints, and relationships
- **📋 Export Capabilities**: Copy table schemas as Markdown and export diagrams
- **🌙 Modern UI**: Clean, responsive interface built with Tailwind CSS
- **💾 Local Processing**: All parsing and rendering happens locally - no server required

## 🛠️ Tech Stack

- **Frontend**: React 18 + Tailwind CSS
- **XML Parsing**: <PERSON><PERSON><PERSON>'s native DOMParser
- **Diagram Rendering**: Mermaid.js
- **Build Tool**: Vite (for future development)
- **Deployment**: Static files served via HTTP server

## 🏃‍♂️ Quick Start

### Prerequisites

- Python 3.x (for local development server)
- Modern web browser with JavaScript enabled

### Running the Application

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd SchemaLens
   ```

2. **Start the development server**
   ```bash
   python -m http.server 8080
   ```

3. **Open in browser**
   Navigate to `http://localhost:8080`

4. **Upload a schema file**
   - Click "Upload XML Schema" or drag and drop an XML file
   - Use the provided `sample-schema.xml` for testing

## 📂 Project Structure

```
SchemaLens/
├── index.html              # Main HTML file with CDN dependencies
├── src/
│   └── app.js              # Complete React application
├── sample-schema.xml       # Example XML schema for testing
├── package.json           # Project configuration
└── README.md              # This file
```

## 📋 XML Schema Format

The application supports XML schemas in the following format:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<database>
  <table name="Customer">
    <column name="CustomerID" type="int" primaryKey="true" nullable="false" description="Unique identifier"/>
    <column name="Name" type="varchar(100)" nullable="false" description="Customer name"/>
    <column name="Email" type="varchar(100)" unique="true" description="Email address"/>
  </table>
  <table name="Order">
    <column name="OrderID" type="int" primaryKey="true" nullable="false"/>
    <column name="CustomerID" type="int" foreignKey="Customer.CustomerID" nullable="false"/>
    <column name="OrderDate" type="datetime" default="CURRENT_TIMESTAMP"/>
  </table>
</database>
```

### Supported Attributes

- **Table**: `name`, `description`
- **Column**: `name`, `type`, `primaryKey`, `foreignKey`, `nullable`, `unique`, `default`, `description`

## 🎯 Usage Guide

### 1. Upload Schema
- Click the "Upload XML Schema" button or drag and drop an XML file
- The application will parse the schema and display the table list

### 2. Explore Tables
- **Table List**: Browse all tables in the left sidebar
- **Search**: Use the search bar to find specific tables or columns
- **Table Details**: Click on a table to view its structure, columns, and relationships

### 3. View Diagrams
- Switch to "Diagram" view to see the ER diagram
- The diagram shows tables, columns, and relationships
- Copy the Mermaid code or export as needed

### 4. Export Data
- **Copy Markdown**: Export table structure as Markdown
- **Copy Diagram Code**: Get the Mermaid diagram code for external use

## 🔧 Development

### Future Enhancements

- **Tauri Integration**: Package as a desktop application
- **Theme Support**: Dark/light mode toggle
- **Advanced Search**: Filter by data types, constraints
- **Schema Validation**: Validate XML schema format
- **Multiple Formats**: Support for other schema formats (JSON, SQL DDL)

### Building for Production

```bash
# Install dependencies (when using Vite)
npm install

# Build for production
npm run build

# Preview production build
npm run preview
```

## 🧪 Testing

The application includes a sample XML schema file (`sample-schema.xml`) that demonstrates:
- Multiple tables with relationships
- Primary and foreign keys
- Various data types and constraints
- Table and column descriptions

## 📝 License

MIT License - see LICENSE file for details

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 🐛 Known Issues

- Large schemas may take time to render in diagram view
- Complex foreign key relationships might need manual adjustment in diagrams
- Browser compatibility: Requires modern browsers with ES6+ support

## 📞 Support

For issues, questions, or contributions, please open an issue in the repository.

---

**SchemaLens** - Making database schema exploration simple and visual! 📊✨
