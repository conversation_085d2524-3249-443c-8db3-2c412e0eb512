<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SchemaLens - Database Schema Explorer</title>
    <meta name="description" content="Interactive database schema explorer and analyzer">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>">
    
    <!-- React and ReactDOM from CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Mermaid for diagrams -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    
    <style>
        /* Custom styles */
        .search-highlight {
            background-color: #fef08a;
            font-weight: 600;
            padding: 1px 2px;
            border-radius: 2px;
        }
        
        .custom-scrollbar {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e1 #f1f5f9;
        }
        
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        /* Dark mode scrollbar */
        .dark .custom-scrollbar {
            scrollbar-color: #4b5563 #374151;
        }
        
        .dark .custom-scrollbar::-webkit-scrollbar-track {
            background: #374151;
        }
        
        .dark .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #4b5563;
        }
        
        .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #6b7280;
        }
        
        /* Animation for loading states */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        /* Print styles */
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                font-size: 12px;
            }
            
            .print-break {
                page-break-before: always;
            }
        }
        
        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .search-highlight {
                background-color: #000;
                color: #fff;
            }
        }
        
        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .transition-all,
            .transition-colors {
                transition: none !important;
            }
            
            .animate-pulse {
                animation: none !important;
            }
        }
    </style>

    <!-- Build Information -->
    <!-- Generated: 2025-07-18T16:24:17.693336 -->
    <!-- Version: 1.0.0 -->
    <!-- Builder: Python Script -->
    </head>
<body class="bg-gray-50 dark:bg-gray-900">
    <div id="root"></div>
    
    <!-- Application JavaScript -->
    <script>

        // SchemaLens Standalone Edition - Complete Application
        // Generated on: 2025-07-18T16:24:17.595902
        
        // Embedded sample schema
        const EMBEDDED_SAMPLE_SCHEMA = `<?xml version="1.0" encoding="UTF-8"?>
<database>
  <table name="Customer" description="Customer information and account details">
    <column name="CustomerID" type="int" primaryKey="true" nullable="false" description="Unique identifier for customer"/>
    <column name="FirstName" type="varchar(50)" nullable="false" description="Customer's first name"/>
    <column name="LastName" type="varchar(50)" nullable="false" description="Customer's last name"/>
    <column name="Email" type="varchar(100)" unique="true" nullable="false" description="Customer's email address"/>
    <column name="Phone" type="varchar(20)" description="Customer's phone number"/>
    <column name="DateCreated" type="datetime" nullable="false" default="CURRENT_TIMESTAMP" description="When the customer record was created"/>
    <column name="IsActive" type="boolean" nullable="false" default="true" description="Whether the customer account is active"/>
    <column name="CustomerType" type="varchar(20)" nullable="false" default="Regular" description="Type of customer (Regular, Premium, VIP)"/>
    <column name="CreditLimit" type="decimal(10,2)" default="1000.00" description="Customer's credit limit"/>
  </table>

  <table name="Category" description="Product categories with hierarchical structure">
    <column name="CategoryID" type="int" primaryKey="true" nullable="false" description="Unique identifier for category"/>
    <column name="CategoryName" type="varchar(100)" nullable="false" unique="true" description="Name of the product category"/>
    <column name="Description" type="text" description="Category description"/>
    <column name="ParentCategoryID" type="int" foreignKey="Category.CategoryID" description="Reference to parent category for hierarchical structure"/>
    <column name="SortOrder" type="int" default="0" description="Display order for categories"/>
    <column name="IsActive" type="boolean" nullable="false" default="true" description="Whether the category is active"/>
  </table>

  <table name="Product" description="Product catalog with pricing and inventory">
    <column name="ProductID" type="int" primaryKey="true" nullable="false" description="Unique identifier for product"/>
    <column name="ProductName" type="varchar(200)" nullable="false" description="Name of the product"/>
    <column name="CategoryID" type="int" foreignKey="Category.CategoryID" nullable="false" description="Reference to product category"/>
    <column name="Price" type="decimal(10,2)" nullable="false" description="Product price"/>
    <column name="StockQuantity" type="int" nullable="false" default="0" description="Current stock quantity"/>
    <column name="SKU" type="varchar(50)" unique="true" nullable="false" description="Stock Keeping Unit"/>
    <column name="Description" type="text" description="Product description"/>
    <column name="IsActive" type="boolean" nullable="false" default="true" description="Whether the product is active"/>
    <column name="DateCreated" type="datetime" nullable="false" default="CURRENT_TIMESTAMP" description="When the product was created"/>
    <column name="Weight" type="decimal(8,2)" description="Product weight in kg"/>
    <column name="Dimensions" type="varchar(50)" description="Product dimensions (LxWxH)"/>
  </table>

  <table name="Order" description="Customer orders and order management">
    <column name="OrderID" type="int" primaryKey="true" nullable="false" description="Unique identifier for order"/>
    <column name="CustomerID" type="int" foreignKey="Customer.CustomerID" nullable="false" description="Reference to customer who placed the order"/>
    <column name="OrderDate" type="datetime" nullable="false" default="CURRENT_TIMESTAMP" description="When the order was placed"/>
    <column name="TotalAmount" type="decimal(10,2)" nullable="false" description="Total order amount"/>
    <column name="Status" type="varchar(20)" nullable="false" default="Pending" description="Order status (Pending, Processing, Shipped, Delivered, Cancelled)"/>
    <column name="ShippingAddress" type="text" nullable="false" description="Shipping address for the order"/>
    <column name="PaymentMethod" type="varchar(50)" description="Payment method used"/>
    <column name="ShippingCost" type="decimal(8,2)" default="0.00" description="Shipping cost for the order"/>
    <column name="TaxAmount" type="decimal(8,2)" default="0.00" description="Tax amount for the order"/>
  </table>

  <table name="OrderItem" description="Individual items within an order">
    <column name="OrderItemID" type="int" primaryKey="true" nullable="false" description="Unique identifier for order item"/>
    <column name="OrderID" type="int" foreignKey="Order.OrderID" nullable="false" description="Reference to the order"/>
    <column name="ProductID" type="int" foreignKey="Product.ProductID" nullable="false" description="Reference to the product"/>
    <column name="Quantity" type="int" nullable="false" description="Quantity of the product ordered"/>
    <column name="UnitPrice" type="decimal(10,2)" nullable="false" description="Price per unit at the time of order"/>
    <column name="TotalPrice" type="decimal(10,2)" nullable="false" description="Total price for this line item"/>
    <column name="DiscountAmount" type="decimal(8,2)" default="0.00" description="Discount applied to this item"/>
  </table>

  <table name="Review" description="Product reviews and ratings from customers">
    <column name="ReviewID" type="int" primaryKey="true" nullable="false" description="Unique identifier for review"/>
    <column name="ProductID" type="int" foreignKey="Product.ProductID" nullable="false" description="Reference to the reviewed product"/>
    <column name="CustomerID" type="int" foreignKey="Customer.CustomerID" nullable="false" description="Reference to customer who wrote the review"/>
    <column name="Rating" type="int" nullable="false" description="Rating from 1 to 5"/>
    <column name="ReviewText" type="text" description="Review content"/>
    <column name="ReviewDate" type="datetime" nullable="false" default="CURRENT_TIMESTAMP" description="When the review was written"/>
    <column name="IsVerified" type="boolean" nullable="false" default="false" description="Whether the review is verified"/>
    <column name="HelpfulVotes" type="int" default="0" description="Number of helpful votes for this review"/>
  </table>

  <table name="Supplier" description="Supplier information and contact details">
    <column name="SupplierID" type="int" primaryKey="true" nullable="false" description="Unique identifier for supplier"/>
    <column name="SupplierName" type="varchar(200)" nullable="false" description="Name of the supplier"/>
    <column name="ContactEmail" type="varchar(100)" unique="true" nullable="false" description="Supplier contact email"/>
    <column name="ContactPhone" type="varchar(20)" description="Supplier contact phone"/>
    <column name="Address" type="text" description="Supplier address"/>
    <column name="IsActive" type="boolean" nullable="false" default="true" description="Whether the supplier is active"/>
    <column name="Rating" type="decimal(3,2)" description="Supplier rating (1.00 to 5.00)"/>
    <column name="PaymentTerms" type="varchar(50)" description="Payment terms with supplier"/>
  </table>

  <table name="ProductSupplier" description="Many-to-many relationship between products and suppliers">
    <column name="ProductSupplierID" type="int" primaryKey="true" nullable="false" description="Unique identifier for product-supplier relationship"/>
    <column name="ProductID" type="int" foreignKey="Product.ProductID" nullable="false" description="Reference to the product"/>
    <column name="SupplierID" type="int" foreignKey="Supplier.SupplierID" nullable="false" description="Reference to the supplier"/>
    <column name="SupplierPrice" type="decimal(10,2)" nullable="false" description="Price from this supplier"/>
    <column name="LeadTimeDays" type="int" description="Lead time in days"/>
    <column name="IsPreferred" type="boolean" nullable="false" default="false" description="Whether this is the preferred supplier for this product"/>
    <column name="MinOrderQuantity" type="int" default="1" description="Minimum order quantity"/>
    <column name="LastOrderDate" type="datetime" description="Date of last order from this supplier"/>
  </table>

  <table name="Inventory" description="Inventory tracking and stock movements">
    <column name="InventoryID" type="int" primaryKey="true" nullable="false" description="Unique identifier for inventory record"/>
    <column name="ProductID" type="int" foreignKey="Product.ProductID" nullable="false" description="Reference to the product"/>
    <column name="MovementType" type="varchar(20)" nullable="false" description="Type of movement (IN, OUT, ADJUSTMENT)"/>
    <column name="Quantity" type="int" nullable="false" description="Quantity moved (positive for IN, negative for OUT)"/>
    <column name="MovementDate" type="datetime" nullable="false" default="CURRENT_TIMESTAMP" description="When the movement occurred"/>
    <column name="Reference" type="varchar(100)" description="Reference to order, purchase, or adjustment"/>
    <column name="Notes" type="text" description="Additional notes about the movement"/>
  </table>
</database>
`;
        
        // Main application code
        // XML Parser utility functions
function parseXMLSchema(xmlText) {
  try {
    const parser = new DOMParser()
    const xmlDoc = parser.parseFromString(xmlText, 'text/xml')
    
    const parseError = xmlDoc.querySelector('parsererror')
    if (parseError) {
      throw new Error('XML parsing error: ' + parseError.textContent)
    }
    
    return extractSchemaData(xmlDoc)
  } catch (error) {
    console.error('XML parsing error:', error)
    throw new Error('Failed to parse XML schema')
  }
}

function extractSchemaData(xmlDoc) {
  const schema = { tables: [], relationships: [] }
  const tableElements = xmlDoc.querySelectorAll('table')
  
  tableElements.forEach(tableElement => {
    const table = parseTable(tableElement)
    if (table) {
      schema.tables.push(table)
      table.columns.forEach(column => {
        if (column.foreignKey) {
          schema.relationships.push({
            from: table.name,
            fromColumn: column.name,
            to: column.foreignKey.table,
            toColumn: column.foreignKey.column,
            type: 'foreign_key'
          })
        }
      })
    }
  })
  
  return schema
}

function parseTable(tableElement) {
  const tableName = tableElement.getAttribute('name')
  if (!tableName) return null

  const table = {
    name: tableName,
    columns: [],
    primaryKeys: [],
    foreignKeys: [],
    constraints: [],
    description: tableElement.getAttribute('description') || ''
  }

  const columnElements = tableElement.querySelectorAll('column')
  columnElements.forEach(columnElement => {
    const column = parseColumn(columnElement)
    if (column) {
      table.columns.push(column)
      if (column.isPrimaryKey) table.primaryKeys.push(column.name)
      if (column.foreignKey) {
        table.foreignKeys.push({
          column: column.name,
          referencedTable: column.foreignKey.table,
          referencedColumn: column.foreignKey.column
        })
      }
    }
  })

  return table
}

function parseColumn(columnElement) {
  const columnName = columnElement.getAttribute('name')
  if (!columnName) return null

  const column = {
    name: columnName,
    type: columnElement.getAttribute('type') || 'varchar',
    nullable: columnElement.getAttribute('nullable') !== 'false',
    isPrimaryKey: columnElement.getAttribute('primaryKey') === 'true',
    isUnique: columnElement.getAttribute('unique') === 'true',
    defaultValue: columnElement.getAttribute('default') || columnElement.getAttribute('defaultValue') || '',
    description: columnElement.getAttribute('description') || '',
    foreignKey: null
  }

  const foreignKeyRef = columnElement.getAttribute('foreignKey')
  if (foreignKeyRef) {
    const fkParts = foreignKeyRef.split('.')
    if (fkParts.length === 2) {
      column.foreignKey = { table: fkParts[0], column: fkParts[1] }
    }
  }

  return column
}

function generateMermaidDiagram(schema) {
  if (!schema || !schema.tables || schema.tables.length === 0) {
    return 'graph TD
    A[No tables found]'
  }

  let mermaidCode = 'erDiagram
'

  // Add tables and their columns
  schema.tables.forEach(table => {
    // Clean table name for Mermaid (remove special characters)
    const cleanTableName = table.name.replace(/[^a-zA-Z0-9_]/g, '_')
    mermaidCode += `    ${cleanTableName} {
`

    table.columns.forEach(column => {
      // Clean column name and type
      const cleanColumnName = column.name.replace(/[^a-zA-Z0-9_]/g, '_')
      const cleanType = column.type.replace(/[()]/g, '').replace(/,/g, '_')

      let columnLine = `        ${cleanType} ${cleanColumnName}`
      if (column.isPrimaryKey) columnLine += ' PK'
      if (column.foreignKey) columnLine += ' FK'
      if (!column.nullable) columnLine += ' "NOT_NULL"'
      if (column.isUnique) columnLine += ' "UNIQUE"'
      mermaidCode += columnLine + '
'
    })
    mermaidCode += '    }
'
  })

  // Add relationships
  if (schema.relationships && schema.relationships.length > 0) {
    mermaidCode += '
'
    schema.relationships.forEach(rel => {
      const cleanFromTable = rel.from.replace(/[^a-zA-Z0-9_]/g, '_')
      const cleanToTable = rel.to.replace(/[^a-zA-Z0-9_]/g, '_')
      const cleanFromCol = rel.fromColumn.replace(/[^a-zA-Z0-9_]/g, '_')
      const cleanToCol = rel.toColumn.replace(/[^a-zA-Z0-9_]/g, '_')
      mermaidCode += `    ${cleanFromTable} ||--o{ ${cleanToTable} : "${cleanFromCol}_to_${cleanToCol}"
`
    })
  }

  return mermaidCode
}

// React Components
const { useState, useEffect, useRef, useMemo } = React

// FileUpload Component
function FileUpload({ onSchemaLoad }) {
  const [isDragging, setIsDragging] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleFileSelect = async (file) => {
    if (!file || !file.name.toLowerCase().endsWith('.xml')) {
      alert('Please select a valid XML file')
      return
    }

    setIsLoading(true)
    try {
      const text = await file.text()
      const parsedSchema = parseXMLSchema(text)
      onSchemaLoad(parsedSchema)
    } catch (error) {
      console.error('Error parsing XML:', error)
      alert('Error parsing XML file. Please check the file format.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDrop = (e) => {
    e.preventDefault()
    setIsDragging(false)
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) handleFileSelect(files[0])
  }

  const handleDragOver = (e) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleFileInput = (e) => {
    const files = Array.from(e.target.files)
    if (files.length > 0) handleFileSelect(files[0])
  }

  return React.createElement('div', { className: 'relative' },
    React.createElement('input', {
      type: 'file',
      accept: '.xml',
      onChange: handleFileInput,
      className: 'hidden',
      id: 'file-upload',
      disabled: isLoading
    }),
    React.createElement('label', {
      htmlFor: 'file-upload',
      className: `inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md transition-colors cursor-pointer ${
        isLoading 
          ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
          : 'bg-blue-600 hover:bg-blue-700 text-white'
      }`,
      onDrop: handleDrop,
      onDragOver: handleDragOver,
      onDragLeave: handleDragLeave
    },
      isLoading ? 'Loading...' : '📁 Upload XML Schema'
    ),
    isDragging && React.createElement('div', {
      className: 'absolute inset-0 bg-blue-100 border-2 border-dashed border-blue-400 rounded-md flex items-center justify-center'
    },
      React.createElement('p', { className: 'text-blue-600 font-medium' }, 'Drop XML file here')
    )
  )
}

// SearchBar Component
function SearchBar({ searchQuery, onSearchChange, schemaData, filters, onFiltersChange }) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [suggestions, setSuggestions] = useState([])
  const [showFilters, setShowFilters] = useState(false)
  const inputRef = useRef(null)

  useEffect(() => {
    if (searchQuery && schemaData?.tables) {
      const newSuggestions = []
      
      schemaData.tables.forEach(table => {
        if (table.name.toLowerCase().includes(searchQuery.toLowerCase())) {
          newSuggestions.push({
            type: 'table',
            name: table.name,
            description: table.description || 'Table',
            table: table.name
          })
        }
        
        table.columns.forEach(column => {
          if (column.name.toLowerCase().includes(searchQuery.toLowerCase())) {
            newSuggestions.push({
              type: 'column',
              name: column.name,
              description: `${column.type} in ${table.name}`,
              table: table.name,
              column: column.name
            })
          }
        })
      })
      
      setSuggestions(newSuggestions.slice(0, 8))
    } else {
      setSuggestions([])
    }
  }, [searchQuery, schemaData])

  const clearSearch = () => {
    onSearchChange('')
    setIsExpanded(false)
    inputRef.current?.focus()
  }

  const clearFilters = () => {
    onFiltersChange({
      dataTypes: [],
      constraints: [],
      hasRelationships: 'all',
      tableType: 'all'
    })
  }

  const toggleFilter = (category, value) => {
    const newFilters = { ...filters }
    if (category === 'dataTypes' || category === 'constraints') {
      const currentValues = newFilters[category] || []
      if (currentValues.includes(value)) {
        newFilters[category] = currentValues.filter(v => v !== value)
      } else {
        newFilters[category] = [...currentValues, value]
      }
    } else {
      newFilters[category] = value
    }
    onFiltersChange(newFilters)
  }

  // Get available data types and constraints from schema
  const availableDataTypes = useMemo(() => {
    if (!schemaData?.tables) return []
    const types = new Set()
    schemaData.tables.forEach(table => {
      table.columns.forEach(column => {
        const baseType = column.type.split('(')[0].toLowerCase()
        types.add(baseType)
      })
    })
    return Array.from(types).sort()
  }, [schemaData])

  const availableConstraints = ['Primary Key', 'Foreign Key', 'Unique', 'Not Null']

  return React.createElement('div', { className: 'relative' },
    React.createElement('div', { className: 'flex items-center space-x-2' },
      React.createElement('div', { className: `flex items-center transition-all duration-200 ${isExpanded ? 'w-80' : 'w-64'}` },
        React.createElement('div', { className: 'relative flex-1' },
          React.createElement('div', { className: 'absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none' },
            React.createElement('svg', { className: 'h-4 w-4 text-gray-400', fill: 'none', stroke: 'currentColor', viewBox: '0 0 24 24' },
              React.createElement('path', { strokeLinecap: 'round', strokeLinejoin: 'round', strokeWidth: 2, d: 'M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z' })
            )
          ),
          React.createElement('input', {
            ref: inputRef,
            type: 'text',
            placeholder: 'Search tables and columns...',
            value: searchQuery,
            onChange: (e) => onSearchChange(e.target.value),
            onFocus: () => setIsExpanded(true),
            onBlur: () => setTimeout(() => setIsExpanded(false), 200),
            className: 'block w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm'
          }),
          searchQuery && React.createElement('button', {
            onClick: clearSearch,
            className: 'absolute inset-y-0 right-0 pr-3 flex items-center'
          },
            React.createElement('svg', { className: 'h-4 w-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300', fill: 'none', stroke: 'currentColor', viewBox: '0 0 24 24' },
              React.createElement('path', { strokeLinecap: 'round', strokeLinejoin: 'round', strokeWidth: 2, d: 'M6 18L18 6M6 6l12 12' })
            )
          )
        )
      ),

      // Filter toggle button
      React.createElement('button', {
        onClick: () => setShowFilters(!showFilters),
        className: `px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium transition-colors ${
          showFilters
            ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 border-blue-300 dark:border-blue-600'
            : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
        }`
      }, '🔧 Filters')
    ),

    // Filter panel
    showFilters && React.createElement('div', {
      className: 'absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 z-50'
    },
      React.createElement('div', { className: 'flex items-center justify-between mb-4' },
        React.createElement('h3', { className: 'text-sm font-semibold text-gray-900 dark:text-white' }, 'Advanced Filters'),
        React.createElement('button', {
          onClick: clearFilters,
          className: 'text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200'
        }, 'Clear All')
      ),

      React.createElement('div', { className: 'grid grid-cols-1 md:grid-cols-2 gap-4' },
        // Data Types filter
        React.createElement('div', null,
          React.createElement('label', { className: 'block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2' }, 'Data Types'),
          React.createElement('div', { className: 'flex flex-wrap gap-1' },
            availableDataTypes.map(type =>
              React.createElement('button', {
                key: type,
                onClick: () => toggleFilter('dataTypes', type),
                className: `px-2 py-1 text-xs rounded transition-colors ${
                  filters.dataTypes?.includes(type)
                    ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 border border-blue-300 dark:border-blue-600'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`
              }, type)
            )
          )
        ),

        // Constraints filter
        React.createElement('div', null,
          React.createElement('label', { className: 'block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2' }, 'Constraints'),
          React.createElement('div', { className: 'flex flex-wrap gap-1' },
            availableConstraints.map(constraint =>
              React.createElement('button', {
                key: constraint,
                onClick: () => toggleFilter('constraints', constraint),
                className: `px-2 py-1 text-xs rounded transition-colors ${
                  filters.constraints?.includes(constraint)
                    ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 border border-green-300 dark:border-green-600'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`
              }, constraint)
            )
          )
        )
      ),

      React.createElement('div', { className: 'grid grid-cols-1 md:grid-cols-2 gap-4 mt-4' },
        // Relationships filter
        React.createElement('div', null,
          React.createElement('label', { className: 'block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2' }, 'Relationships'),
          React.createElement('select', {
            value: filters.hasRelationships || 'all',
            onChange: (e) => toggleFilter('hasRelationships', e.target.value),
            className: 'w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white'
          },
            React.createElement('option', { value: 'all' }, 'All Tables'),
            React.createElement('option', { value: 'with' }, 'With Relationships'),
            React.createElement('option', { value: 'without' }, 'Without Relationships')
          )
        ),

        // Table type filter
        React.createElement('div', null,
          React.createElement('label', { className: 'block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2' }, 'Table Type'),
          React.createElement('select', {
            value: filters.tableType || 'all',
            onChange: (e) => toggleFilter('tableType', e.target.value),
            className: 'w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white'
          },
            React.createElement('option', { value: 'all' }, 'All Types'),
            React.createElement('option', { value: 'large' }, 'Large Tables (5+ columns)'),
            React.createElement('option', { value: 'small' }, 'Small Tables (<5 columns)'),
            React.createElement('option', { value: 'junction' }, 'Junction Tables')
          )
        )
      )
    )
  )
}

// TableExplorer Component
function TableExplorer({ schemaData, selectedTable, onTableSelect, searchQuery, filters }) {
  const filteredTables = useMemo(() => {
    if (!schemaData?.tables) return []

    let tables = schemaData.tables

    // Apply text search
    if (searchQuery) {
      tables = tables.filter(table => {
        if (table.name.toLowerCase().includes(searchQuery.toLowerCase())) return true
        if (table.description?.toLowerCase().includes(searchQuery.toLowerCase())) return true
        return table.columns.some(column =>
          column.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          column.description?.toLowerCase().includes(searchQuery.toLowerCase())
        )
      })
    }

    // Apply filters
    if (filters) {
      // Data type filter
      if (filters.dataTypes?.length > 0) {
        tables = tables.filter(table =>
          table.columns.some(column => {
            const baseType = column.type.split('(')[0].toLowerCase()
            return filters.dataTypes.includes(baseType)
          })
        )
      }

      // Constraints filter
      if (filters.constraints?.length > 0) {
        tables = tables.filter(table =>
          table.columns.some(column => {
            return filters.constraints.some(constraint => {
              switch (constraint) {
                case 'Primary Key': return column.isPrimaryKey
                case 'Foreign Key': return !!column.foreignKey
                case 'Unique': return column.isUnique
                case 'Not Null': return !column.nullable
                default: return false
              }
            })
          })
        )
      }

      // Relationships filter
      if (filters.hasRelationships && filters.hasRelationships !== 'all') {
        tables = tables.filter(table => {
          const hasRelationships = table.foreignKeys.length > 0 ||
            schemaData.relationships.some(rel => rel.to === table.name)
          return filters.hasRelationships === 'with' ? hasRelationships : !hasRelationships
        })
      }

      // Table type filter
      if (filters.tableType && filters.tableType !== 'all') {
        tables = tables.filter(table => {
          const columnCount = table.columns.length
          const isJunction = table.foreignKeys.length >= 2 && columnCount <= 4

          switch (filters.tableType) {
            case 'large': return columnCount >= 5
            case 'small': return columnCount < 5 && !isJunction
            case 'junction': return isJunction
            default: return true
          }
        })
      }
    }

    return tables
  }, [schemaData?.tables, searchQuery, filters])

  return React.createElement('div', { className: 'flex-1 flex' },
    // Sidebar
    React.createElement('div', { className: 'w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col' },
      React.createElement('div', { className: 'p-4 border-b border-gray-200 dark:border-gray-700' },
        React.createElement('h2', { className: 'text-lg font-semibold text-gray-900 dark:text-white' },
          `Tables (${filteredTables.length})`
        ),
        searchQuery && React.createElement('p', { className: 'text-sm text-gray-600 dark:text-gray-400 mt-1' },
          `Filtered by: "${searchQuery}"`
        )
      ),
      React.createElement('div', { className: 'flex-1 overflow-hidden' },
        React.createElement(TableList, {
          tables: filteredTables,
          selectedTable,
          onTableSelect,
          searchQuery
        })
      )
    ),

    // Main Content
    React.createElement('div', { className: 'flex-1 flex flex-col' },
      selectedTable ?
        React.createElement(TableDetail, { table: selectedTable, schemaData, searchQuery }) :
        React.createElement('div', { className: 'flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900' },
          React.createElement('div', { className: 'text-center' },
            React.createElement('div', { className: 'text-6xl mb-4' }, '🗂️'),
            React.createElement('h3', { className: 'text-xl font-semibold text-gray-900 dark:text-white mb-2' },
              'Select a Table'
            ),
            React.createElement('p', { className: 'text-gray-600 dark:text-gray-400 max-w-md' },
              'Choose a table from the sidebar to view its structure, columns, relationships, and constraints.'
            )
          )
        )
    )
  )
}

// TableList Component
function TableList({ tables, selectedTable, onTableSelect, searchQuery }) {
  const highlightText = (text, query) => {
    if (!query) return text
    const regex = new RegExp(`(${query})`, 'gi')
    const parts = text.split(regex)
    return parts.map((part, index) =>
      regex.test(part) ? React.createElement('span', { key: index, className: 'search-highlight' }, part) : part
    )
  }

  return React.createElement('div', { className: 'custom-scrollbar overflow-y-auto h-full' },
    tables.length === 0 ?
      React.createElement('div', { className: 'p-4 text-center text-gray-500 dark:text-gray-400' },
        searchQuery ? 'No tables match your search' : 'No tables found'
      ) :
      React.createElement('div', { className: 'p-2' },
        tables.map((table) => {
          const isSelected = selectedTable?.name === table.name
          return React.createElement('div', {
            key: table.name,
            className: `p-3 mb-2 rounded-lg cursor-pointer transition-all duration-200 ${
              isSelected
                ? 'bg-blue-100 dark:bg-blue-900 border-2 border-blue-300 dark:border-blue-600'
                : 'bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 border-2 border-transparent'
            }`,
            onClick: () => onTableSelect(table)
          },
            React.createElement('div', { className: 'flex items-center justify-between' },
              React.createElement('h3', {
                className: `font-medium ${isSelected ? 'text-blue-900 dark:text-blue-100' : 'text-gray-900 dark:text-white'}`
              }, highlightText(table.name, searchQuery)),
              React.createElement('span', {
                className: `text-xs px-2 py-1 rounded-full ${
                  isSelected
                    ? 'bg-blue-200 dark:bg-blue-800 text-blue-800 dark:text-blue-200'
                    : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300'
                }`
              }, `${table.columns.length} cols`)
            ),
            table.description && React.createElement('p', {
              className: `text-sm mt-1 ${isSelected ? 'text-blue-700 dark:text-blue-300' : 'text-gray-600 dark:text-gray-400'}`
            }, table.description),
            table.primaryKeys.length > 0 && React.createElement('div', { className: 'mt-2' },
              React.createElement('span', {
                className: `text-xs font-medium ${isSelected ? 'text-blue-800 dark:text-blue-200' : 'text-gray-700 dark:text-gray-300'}`
              }, `PK: ${table.primaryKeys.join(', ')}`)
            )
          )
        })
      )
  )
}

// TableDetail Component
function TableDetail({ table, schemaData, searchQuery }) {
  const [activeTab, setActiveTab] = useState('columns')

  const highlightText = (text, query) => {
    if (!query) return text
    const regex = new RegExp(`(${query})`, 'gi')
    const parts = text.split(regex)
    return parts.map((part, index) =>
      regex.test(part) ? React.createElement('span', { key: index, className: 'search-highlight' }, part) : part
    )
  }

  const exportTableMarkdown = () => {
    let markdown = `# ${table.name}

`
    if (table.description) markdown += `${table.description}

`
    markdown += `## Columns

| Column | Type | Nullable | Primary Key | Foreign Key | Unique | Default |
|--------|------|----------|-------------|-------------|--------|---------|
`

    table.columns.forEach(column => {
      const fkRef = column.foreignKey ? `${column.foreignKey.table}.${column.foreignKey.column}` : ''
      markdown += `| ${column.name} | ${column.type} | ${column.nullable ? 'Yes' : 'No'} | ${column.isPrimaryKey ? 'Yes' : 'No'} | ${fkRef} | ${column.isUnique ? 'Yes' : 'No'} | ${column.defaultValue || ''} |
`
    })

    navigator.clipboard.writeText(markdown)
  }

  return React.createElement('div', { className: 'flex-1 flex flex-col bg-white dark:bg-gray-800' },
    // Header
    React.createElement('div', { className: 'p-6 border-b border-gray-200 dark:border-gray-700' },
      React.createElement('div', { className: 'flex items-center justify-between' },
        React.createElement('div', null,
          React.createElement('h1', { className: 'text-2xl font-bold text-gray-900 dark:text-white' },
            highlightText(table.name, searchQuery)
          ),
          table.description && React.createElement('p', { className: 'text-gray-600 dark:text-gray-400 mt-1' }, table.description)
        ),
        React.createElement('div', { className: 'flex space-x-2' },
          React.createElement('button', {
            onClick: exportTableMarkdown,
            className: 'px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors'
          }, '📋 Copy Markdown')
        )
      ),
      React.createElement('div', { className: 'flex space-x-6 mt-4 text-sm text-gray-600 dark:text-gray-400' },
        React.createElement('span', null, `${table.columns.length} columns`),
        React.createElement('span', null, `${table.primaryKeys.length} primary key${table.primaryKeys.length !== 1 ? 's' : ''}`),
        React.createElement('span', null, `${table.foreignKeys.length} foreign key${table.foreignKeys.length !== 1 ? 's' : ''}`)
      )
    ),

    // Tabs
    React.createElement('div', { className: 'border-b border-gray-200 dark:border-gray-700' },
      React.createElement('nav', { className: 'flex space-x-8 px-6' },
        ['columns', 'relationships'].map((tab) =>
          React.createElement('button', {
            key: tab,
            onClick: () => setActiveTab(tab),
            className: `py-4 px-1 border-b-2 font-medium text-sm capitalize transition-colors ${
              activeTab === tab
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`
          }, tab)
        )
      )
    ),

    // Tab Content
    React.createElement('div', { className: 'flex-1 overflow-auto custom-scrollbar' },
      activeTab === 'columns' && React.createElement('div', { className: 'p-6' },
        React.createElement('div', { className: 'overflow-x-auto' },
          React.createElement('table', { className: 'min-w-full divide-y divide-gray-200 dark:divide-gray-700' },
            React.createElement('thead', { className: 'bg-gray-50 dark:bg-gray-700' },
              React.createElement('tr', null,
                ['Column', 'Type', 'Constraints', 'Default', 'Description'].map(header =>
                  React.createElement('th', {
                    key: header,
                    className: 'px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider'
                  }, header)
                )
              )
            ),
            React.createElement('tbody', { className: 'bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700' },
              table.columns.map((column) =>
                React.createElement('tr', { key: column.name, className: 'hover:bg-gray-50 dark:hover:bg-gray-700' },
                  React.createElement('td', { className: 'px-6 py-4 whitespace-nowrap' },
                    React.createElement('div', { className: 'flex items-center' },
                      React.createElement('span', { className: 'text-sm font-medium text-gray-900 dark:text-white' },
                        highlightText(column.name, searchQuery)
                      ),
                      column.isPrimaryKey && React.createElement('span', {
                        className: 'ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                      }, 'PK'),
                      column.foreignKey && React.createElement('span', {
                        className: 'ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                      }, 'FK')
                    )
                  ),
                  React.createElement('td', { className: 'px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white' }, column.type),
                  React.createElement('td', { className: 'px-6 py-4 whitespace-nowrap' },
                    React.createElement('div', { className: 'flex flex-wrap gap-1' },
                      !column.nullable && React.createElement('span', {
                        className: 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                      }, 'NOT NULL'),
                      column.isUnique && React.createElement('span', {
                        className: 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      }, 'UNIQUE')
                    )
                  ),
                  React.createElement('td', { className: 'px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400' }, column.defaultValue || '-'),
                  React.createElement('td', { className: 'px-6 py-4 text-sm text-gray-500 dark:text-gray-400' }, column.description || '-')
                )
              )
            )
          )
        )
      ),

      activeTab === 'relationships' && React.createElement('div', { className: 'p-6' },
        React.createElement('div', { className: 'space-y-6' },
          table.foreignKeys.length > 0 && React.createElement('div', null,
            React.createElement('h3', { className: 'text-lg font-medium text-gray-900 dark:text-white mb-4' },
              'References (Foreign Keys)'
            ),
            React.createElement('div', { className: 'space-y-3' },
              table.foreignKeys.map((fk, index) =>
                React.createElement('div', { key: index, className: 'bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg' },
                  React.createElement('div', { className: 'flex items-center' },
                    React.createElement('span', { className: 'text-sm font-medium text-gray-900 dark:text-white' }, fk.column),
                    React.createElement('span', { className: 'mx-2 text-gray-500' }, '→'),
                    React.createElement('span', { className: 'text-sm font-medium text-blue-600 dark:text-blue-400' },
                      `${fk.referencedTable}.${fk.referencedColumn}`
                    )
                  )
                )
              )
            )
          ),

          table.foreignKeys.length === 0 && React.createElement('div', { className: 'text-center py-8 text-gray-500 dark:text-gray-400' },
            'No relationships found for this table'
          )
        )
      )
    )
  )
}

// StatisticsView Component
function StatisticsView({ schemaData }) {
  const stats = useMemo(() => {
    if (!schemaData?.tables) return null

    const tables = schemaData.tables
    const totalTables = tables.length
    const totalColumns = tables.reduce((sum, table) => sum + table.columns.length, 0)
    const totalRelationships = schemaData.relationships?.length || 0

    // Data type distribution
    const dataTypeCount = {}
    tables.forEach(table => {
      table.columns.forEach(column => {
        const baseType = column.type.split('(')[0].toLowerCase()
        dataTypeCount[baseType] = (dataTypeCount[baseType] || 0) + 1
      })
    })

    // Constraint analysis
    const constraintStats = {
      primaryKeys: 0,
      foreignKeys: 0,
      uniqueConstraints: 0,
      notNullConstraints: 0
    }

    tables.forEach(table => {
      table.columns.forEach(column => {
        if (column.isPrimaryKey) constraintStats.primaryKeys++
        if (column.foreignKey) constraintStats.foreignKeys++
        if (column.isUnique) constraintStats.uniqueConstraints++
        if (!column.nullable) constraintStats.notNullConstraints++
      })
    })

    // Table size analysis
    const tableSizes = tables.map(table => ({
      name: table.name,
      columnCount: table.columns.length,
      relationshipCount: table.foreignKeys.length +
        schemaData.relationships.filter(rel => rel.to === table.name).length
    }))

    const avgColumnsPerTable = totalColumns / totalTables
    const maxColumns = Math.max(...tableSizes.map(t => t.columnCount))
    const minColumns = Math.min(...tableSizes.map(t => t.columnCount))

    // Relationship complexity
    const tablesWithRelationships = tables.filter(table =>
      table.foreignKeys.length > 0 ||
      schemaData.relationships.some(rel => rel.to === table.name)
    ).length

    const relationshipDensity = totalRelationships / totalTables

    // Schema health metrics
    const tablesWithoutPK = tables.filter(table => table.primaryKeys.length === 0).length
    const tablesWithoutDescription = tables.filter(table => !table.description).length
    const columnsWithoutDescription = tables.reduce((sum, table) =>
      sum + table.columns.filter(col => !col.description).length, 0
    )

    // Audit trail analysis
    const auditColumns = {
      insertUser: ['insert_user', 'insertuser', 'created_by', 'createdby', 'creator', 'insert_by'],
      insertDate: ['insert_date', 'insertdate', 'created_at', 'createdat', 'created_date', 'createddate', 'date_created', 'datecreated'],
      updateUser: ['update_user', 'updateuser', 'updated_by', 'updatedby', 'modifier', 'modified_by', 'update_by'],
      updateDate: ['update_date', 'updatedate', 'updated_at', 'updatedat', 'updated_date', 'updateddate', 'date_updated', 'dateupdated', 'modified_at', 'modifiedat'],
      active: ['active', 'is_active', 'isactive', 'enabled', 'is_enabled', 'isenabled', 'status', 'is_deleted', 'isdeleted', 'deleted']
    }

    const auditAnalysis = {
      tablesWithoutInsertUser: [],
      tablesWithoutInsertDate: [],
      tablesWithoutUpdateUser: [],
      tablesWithoutUpdateDate: [],
      tablesWithoutActive: [],
      tablesWithAllAuditColumns: [],
      tablesWithNoAuditColumns: []
    }

    tables.forEach(table => {
      const columnNames = table.columns.map(col => col.name.toLowerCase())

      const hasInsertUser = auditColumns.insertUser.some(pattern =>
        columnNames.some(name => name.includes(pattern))
      )
      const hasInsertDate = auditColumns.insertDate.some(pattern =>
        columnNames.some(name => name.includes(pattern))
      )
      const hasUpdateUser = auditColumns.updateUser.some(pattern =>
        columnNames.some(name => name.includes(pattern))
      )
      const hasUpdateDate = auditColumns.updateDate.some(pattern =>
        columnNames.some(name => name.includes(pattern))
      )
      const hasActive = auditColumns.active.some(pattern =>
        columnNames.some(name => name.includes(pattern))
      )

      if (!hasInsertUser) auditAnalysis.tablesWithoutInsertUser.push(table.name)
      if (!hasInsertDate) auditAnalysis.tablesWithoutInsertDate.push(table.name)
      if (!hasUpdateUser) auditAnalysis.tablesWithoutUpdateUser.push(table.name)
      if (!hasUpdateDate) auditAnalysis.tablesWithoutUpdateDate.push(table.name)
      if (!hasActive) auditAnalysis.tablesWithoutActive.push(table.name)

      if (hasInsertUser && hasInsertDate && hasUpdateUser && hasUpdateDate && hasActive) {
        auditAnalysis.tablesWithAllAuditColumns.push(table.name)
      }

      if (!hasInsertUser && !hasInsertDate && !hasUpdateUser && !hasUpdateDate && !hasActive) {
        auditAnalysis.tablesWithNoAuditColumns.push(table.name)
      }
    })

    const auditCompliance = Math.round((auditAnalysis.tablesWithAllAuditColumns.length / totalTables) * 100)

    return {
      overview: {
        totalTables,
        totalColumns,
        totalRelationships,
        avgColumnsPerTable: Math.round(avgColumnsPerTable * 10) / 10,
        maxColumns,
        minColumns
      },
      dataTypes: dataTypeCount,
      constraints: constraintStats,
      tableSizes,
      relationships: {
        tablesWithRelationships,
        relationshipDensity: Math.round(relationshipDensity * 100) / 100,
        isolatedTables: totalTables - tablesWithRelationships
      },
      health: {
        tablesWithoutPK,
        tablesWithoutDescription,
        columnsWithoutDescription,
        healthScore: Math.round(((totalTables - tablesWithoutPK) / totalTables +
          (totalTables - tablesWithoutDescription) / totalTables +
          (totalColumns - columnsWithoutDescription) / totalColumns) / 3 * 100)
      },
      audit: {
        ...auditAnalysis,
        auditCompliance,
        totalTablesWithAudit: auditAnalysis.tablesWithAllAuditColumns.length,
        totalTablesWithoutAudit: auditAnalysis.tablesWithNoAuditColumns.length
      }
    }
  }, [schemaData])

  if (!schemaData || !stats) {
    return React.createElement('div', { className: 'flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900' },
      React.createElement('div', { className: 'text-center' },
        React.createElement('div', { className: 'text-6xl mb-4' }, '📊'),
        React.createElement('h3', { className: 'text-xl font-semibold text-gray-900 dark:text-white mb-2' },
          'No Schema Loaded'
        ),
        React.createElement('p', { className: 'text-gray-600 dark:text-gray-400' },
          'Upload an XML schema file to view statistics'
        )
      )
    )
  }

  return React.createElement('div', { className: 'flex-1 bg-gray-50 dark:bg-gray-900 overflow-auto' },
    React.createElement('div', { className: 'p-6 max-w-7xl mx-auto' },
      // Header
      React.createElement('div', { className: 'mb-8' },
        React.createElement('h1', { className: 'text-3xl font-bold text-gray-900 dark:text-white mb-2' },
          'Schema Statistics'
        ),
        React.createElement('p', { className: 'text-gray-600 dark:text-gray-400' },
          'Comprehensive analysis of your database schema structure and health'
        )
      ),

      // Overview Cards
      React.createElement('div', { className: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8' },
        React.createElement('div', { className: 'bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700' },
          React.createElement('div', { className: 'flex items-center' },
            React.createElement('div', { className: 'text-3xl mr-4' }, '🗂️'),
            React.createElement('div', null,
              React.createElement('p', { className: 'text-2xl font-bold text-gray-900 dark:text-white' }, stats.overview.totalTables),
              React.createElement('p', { className: 'text-sm text-gray-600 dark:text-gray-400' }, 'Tables')
            )
          )
        ),
        React.createElement('div', { className: 'bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700' },
          React.createElement('div', { className: 'flex items-center' },
            React.createElement('div', { className: 'text-3xl mr-4' }, '📋'),
            React.createElement('div', null,
              React.createElement('p', { className: 'text-2xl font-bold text-gray-900 dark:text-white' }, stats.overview.totalColumns),
              React.createElement('p', { className: 'text-sm text-gray-600 dark:text-gray-400' }, 'Columns')
            )
          )
        ),
        React.createElement('div', { className: 'bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700' },
          React.createElement('div', { className: 'flex items-center' },
            React.createElement('div', { className: 'text-3xl mr-4' }, '🔗'),
            React.createElement('div', null,
              React.createElement('p', { className: 'text-2xl font-bold text-gray-900 dark:text-white' }, stats.overview.totalRelationships),
              React.createElement('p', { className: 'text-sm text-gray-600 dark:text-gray-400' }, 'Relationships')
            )
          )
        ),
        React.createElement('div', { className: 'bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700' },
          React.createElement('div', { className: 'flex items-center' },
            React.createElement('div', { className: 'text-3xl mr-4' }, '💚'),
            React.createElement('div', null,
              React.createElement('p', { className: 'text-2xl font-bold text-gray-900 dark:text-white' }, `${stats.health.healthScore}%`),
              React.createElement('p', { className: 'text-sm text-gray-600 dark:text-gray-400' }, 'Health Score')
            )
          )
        )
      ),

      // Detailed Statistics Grid
      React.createElement('div', { className: 'grid grid-cols-1 lg:grid-cols-2 gap-8' },
        // Data Types Distribution
        React.createElement('div', { className: 'bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700' },
          React.createElement('h3', { className: 'text-lg font-semibold text-gray-900 dark:text-white mb-4' },
            '📊 Data Type Distribution'
          ),
          React.createElement('div', { className: 'space-y-3' },
            Object.entries(stats.dataTypes)
              .sort(([,a], [,b]) => b - a)
              .slice(0, 8)
              .map(([type, count]) => {
                const percentage = Math.round((count / stats.overview.totalColumns) * 100)
                return React.createElement('div', { key: type, className: 'flex items-center justify-between' },
                  React.createElement('div', { className: 'flex items-center' },
                    React.createElement('span', { className: 'text-sm font-medium text-gray-900 dark:text-white capitalize' }, type),
                    React.createElement('span', { className: 'ml-2 text-xs text-gray-500 dark:text-gray-400' }, `${count} columns`)
                  ),
                  React.createElement('div', { className: 'flex items-center' },
                    React.createElement('div', {
                      className: 'w-20 h-2 bg-gray-200 dark:bg-gray-700 rounded-full mr-2'
                    },
                      React.createElement('div', {
                        className: 'h-2 bg-blue-500 rounded-full',
                        style: { width: `${percentage}%` }
                      })
                    ),
                    React.createElement('span', { className: 'text-xs text-gray-600 dark:text-gray-400 w-8' }, `${percentage}%`)
                  )
                )
              })
          )
        ),

        // Constraints Analysis
        React.createElement('div', { className: 'bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700' },
          React.createElement('h3', { className: 'text-lg font-semibold text-gray-900 dark:text-white mb-4' },
            '🔒 Constraints Analysis'
          ),
          React.createElement('div', { className: 'space-y-4' },
            [
              { label: 'Primary Keys', value: stats.constraints.primaryKeys, icon: '🔑', color: 'yellow' },
              { label: 'Foreign Keys', value: stats.constraints.foreignKeys, icon: '🔗', color: 'blue' },
              { label: 'Unique Constraints', value: stats.constraints.uniqueConstraints, icon: '⭐', color: 'green' },
              { label: 'Not Null Constraints', value: stats.constraints.notNullConstraints, icon: '🚫', color: 'red' }
            ].map(constraint =>
              React.createElement('div', { key: constraint.label, className: 'flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded' },
                React.createElement('div', { className: 'flex items-center' },
                  React.createElement('span', { className: 'text-lg mr-3' }, constraint.icon),
                  React.createElement('span', { className: 'text-sm font-medium text-gray-900 dark:text-white' }, constraint.label)
                ),
                React.createElement('span', {
                  className: `px-2 py-1 text-xs font-semibold rounded-full bg-${constraint.color}-100 text-${constraint.color}-800 dark:bg-${constraint.color}-900 dark:text-${constraint.color}-200`
                }, constraint.value)
              )
            )
          )
        ),

        // Table Size Analysis
        React.createElement('div', { className: 'bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700' },
          React.createElement('h3', { className: 'text-lg font-semibold text-gray-900 dark:text-white mb-4' },
            '📏 Table Size Analysis'
          ),
          React.createElement('div', { className: 'space-y-3' },
            React.createElement('div', { className: 'grid grid-cols-3 gap-4 text-center' },
              React.createElement('div', { className: 'p-3 bg-blue-50 dark:bg-blue-900/20 rounded' },
                React.createElement('p', { className: 'text-lg font-bold text-blue-600 dark:text-blue-400' }, stats.overview.avgColumnsPerTable),
                React.createElement('p', { className: 'text-xs text-gray-600 dark:text-gray-400' }, 'Avg Columns')
              ),
              React.createElement('div', { className: 'p-3 bg-green-50 dark:bg-green-900/20 rounded' },
                React.createElement('p', { className: 'text-lg font-bold text-green-600 dark:text-green-400' }, stats.overview.maxColumns),
                React.createElement('p', { className: 'text-xs text-gray-600 dark:text-gray-400' }, 'Max Columns')
              ),
              React.createElement('div', { className: 'p-3 bg-orange-50 dark:bg-orange-900/20 rounded' },
                React.createElement('p', { className: 'text-lg font-bold text-orange-600 dark:text-orange-400' }, stats.overview.minColumns),
                React.createElement('p', { className: 'text-xs text-gray-600 dark:text-gray-400' }, 'Min Columns')
              )
            ),
            React.createElement('div', { className: 'mt-4' },
              React.createElement('h4', { className: 'text-sm font-medium text-gray-900 dark:text-white mb-2' }, 'Largest Tables'),
              React.createElement('div', { className: 'space-y-1' },
                stats.tableSizes
                  .sort((a, b) => b.columnCount - a.columnCount)
                  .slice(0, 5)
                  .map(table =>
                    React.createElement('div', { key: table.name, className: 'flex justify-between text-sm' },
                      React.createElement('span', { className: 'text-gray-900 dark:text-white' }, table.name),
                      React.createElement('span', { className: 'text-gray-600 dark:text-gray-400' }, `${table.columnCount} cols`)
                    )
                  )
              )
            )
          )
        ),

        // Schema Health
        React.createElement('div', { className: 'bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700' },
          React.createElement('h3', { className: 'text-lg font-semibold text-gray-900 dark:text-white mb-4' },
            '🏥 Schema Health'
          ),
          React.createElement('div', { className: 'space-y-4' },
            React.createElement('div', { className: 'flex items-center justify-between' },
              React.createElement('span', { className: 'text-sm text-gray-900 dark:text-white' }, 'Overall Health Score'),
              React.createElement('div', { className: 'flex items-center' },
                React.createElement('div', { className: 'w-24 h-2 bg-gray-200 dark:bg-gray-700 rounded-full mr-2' },
                  React.createElement('div', {
                    className: `h-2 rounded-full ${stats.health.healthScore >= 80 ? 'bg-green-500' : stats.health.healthScore >= 60 ? 'bg-yellow-500' : 'bg-red-500'}`,
                    style: { width: `${stats.health.healthScore}%` }
                  })
                ),
                React.createElement('span', { className: 'text-sm font-semibold text-gray-900 dark:text-white' }, `${stats.health.healthScore}%`)
              )
            ),
            React.createElement('div', { className: 'space-y-2 text-sm' },
              React.createElement('div', { className: 'flex justify-between' },
                React.createElement('span', { className: 'text-gray-600 dark:text-gray-400' }, 'Tables without Primary Key'),
                React.createElement('span', { className: `font-medium ${stats.health.tablesWithoutPK > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}` },
                  stats.health.tablesWithoutPK
                )
              ),
              React.createElement('div', { className: 'flex justify-between' },
                React.createElement('span', { className: 'text-gray-600 dark:text-gray-400' }, 'Tables without Description'),
                React.createElement('span', { className: `font-medium ${stats.health.tablesWithoutDescription > 0 ? 'text-yellow-600 dark:text-yellow-400' : 'text-green-600 dark:text-green-400'}` },
                  stats.health.tablesWithoutDescription
                )
              ),
              React.createElement('div', { className: 'flex justify-between' },
                React.createElement('span', { className: 'text-gray-600 dark:text-gray-400' }, 'Columns without Description'),
                React.createElement('span', { className: `font-medium ${stats.health.columnsWithoutDescription > 0 ? 'text-yellow-600 dark:text-yellow-400' : 'text-green-600 dark:text-green-400'}` },
                  stats.health.columnsWithoutDescription
                )
              )
            )
          )
        )
      ),

      // Audit Trail Analysis Section
      React.createElement('div', { className: 'mt-8' },
        React.createElement('h2', { className: 'text-2xl font-bold text-gray-900 dark:text-white mb-6' },
          '🔍 Audit Trail & Data Governance Analysis'
        ),

        // Audit Overview Cards
        React.createElement('div', { className: 'grid grid-cols-1 md:grid-cols-3 gap-6 mb-6' },
          React.createElement('div', { className: 'bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700' },
            React.createElement('div', { className: 'flex items-center' },
              React.createElement('div', { className: 'text-3xl mr-4' }, '✅'),
              React.createElement('div', null,
                React.createElement('p', { className: 'text-2xl font-bold text-green-600 dark:text-green-400' }, stats.audit.totalTablesWithAudit),
                React.createElement('p', { className: 'text-sm text-gray-600 dark:text-gray-400' }, 'Tables with Full Audit')
              )
            )
          ),
          React.createElement('div', { className: 'bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700' },
            React.createElement('div', { className: 'flex items-center' },
              React.createElement('div', { className: 'text-3xl mr-4' }, '❌'),
              React.createElement('div', null,
                React.createElement('p', { className: 'text-2xl font-bold text-red-600 dark:text-red-400' }, stats.audit.totalTablesWithoutAudit),
                React.createElement('p', { className: 'text-sm text-gray-600 dark:text-gray-400' }, 'Tables with No Audit')
              )
            )
          ),
          React.createElement('div', { className: 'bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700' },
            React.createElement('div', { className: 'flex items-center' },
              React.createElement('div', { className: 'text-3xl mr-4' }, '📊'),
              React.createElement('div', null,
                React.createElement('p', { className: 'text-2xl font-bold text-blue-600 dark:text-blue-400' }, `${stats.audit.auditCompliance}%`),
                React.createElement('p', { className: 'text-sm text-gray-600 dark:text-gray-400' }, 'Audit Compliance')
              )
            )
          )
        ),

        // Detailed Audit Analysis
        React.createElement('div', { className: 'grid grid-cols-1 lg:grid-cols-2 gap-8' },
          // Missing Audit Columns
          React.createElement('div', { className: 'bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700' },
            React.createElement('h3', { className: 'text-lg font-semibold text-gray-900 dark:text-white mb-4' },
              '⚠️ Missing Audit Columns'
            ),
            React.createElement('div', { className: 'space-y-4' },
              [
                { label: 'Insert User', tables: stats.audit.tablesWithoutInsertUser, icon: '👤', color: 'red' },
                { label: 'Insert Date', tables: stats.audit.tablesWithoutInsertDate, icon: '📅', color: 'red' },
                { label: 'Update User', tables: stats.audit.tablesWithoutUpdateUser, icon: '✏️', color: 'orange' },
                { label: 'Update Date', tables: stats.audit.tablesWithoutUpdateDate, icon: '🕒', color: 'orange' },
                { label: 'Active/Status', tables: stats.audit.tablesWithoutActive, icon: '🔄', color: 'yellow' }
              ].map(audit =>
                React.createElement('div', { key: audit.label, className: 'border-l-4 border-red-400 pl-4' },
                  React.createElement('div', { className: 'flex items-center justify-between mb-2' },
                    React.createElement('div', { className: 'flex items-center' },
                      React.createElement('span', { className: 'text-lg mr-2' }, audit.icon),
                      React.createElement('span', { className: 'font-medium text-gray-900 dark:text-white' }, audit.label),
                      React.createElement('span', {
                        className: `ml-2 px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200`
                      }, `${audit.tables.length} missing`)
                    )
                  ),
                  audit.tables.length > 0 && React.createElement('div', { className: 'text-sm text-gray-600 dark:text-gray-400' },
                    React.createElement('details', null,
                      React.createElement('summary', { className: 'cursor-pointer hover:text-gray-800 dark:hover:text-gray-200' },
                        `Show ${audit.tables.length} tables`
                      ),
                      React.createElement('div', { className: 'mt-2 pl-4 space-y-1' },
                        audit.tables.slice(0, 10).map(tableName =>
                          React.createElement('div', { key: tableName, className: 'text-xs' }, `• ${tableName}`)
                        ),
                        audit.tables.length > 10 && React.createElement('div', { className: 'text-xs text-gray-500' },
                          `... and ${audit.tables.length - 10} more`
                        )
                      )
                    )
                  )
                )
              )
            )
          ),

          // Audit Compliance Summary
          React.createElement('div', { className: 'bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700' },
            React.createElement('h3', { className: 'text-lg font-semibold text-gray-900 dark:text-white mb-4' },
              '📋 Audit Compliance Summary'
            ),
            React.createElement('div', { className: 'space-y-4' },
              // Compliance Score
              React.createElement('div', { className: 'p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg' },
                React.createElement('div', { className: 'flex items-center justify-between mb-2' },
                  React.createElement('span', { className: 'font-medium text-gray-900 dark:text-white' }, 'Overall Audit Compliance'),
                  React.createElement('span', { className: 'text-lg font-bold text-blue-600 dark:text-blue-400' }, `${stats.audit.auditCompliance}%`)
                ),
                React.createElement('div', { className: 'w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2' },
                  React.createElement('div', {
                    className: `h-2 rounded-full ${stats.audit.auditCompliance >= 80 ? 'bg-green-500' : stats.audit.auditCompliance >= 60 ? 'bg-yellow-500' : 'bg-red-500'}`,
                    style: { width: `${stats.audit.auditCompliance}%` }
                  })
                )
              ),

              // Tables with Full Audit
              stats.audit.tablesWithAllAuditColumns.length > 0 && React.createElement('div', null,
                React.createElement('h4', { className: 'font-medium text-green-600 dark:text-green-400 mb-2' },
                  `✅ Tables with Complete Audit Trail (${stats.audit.tablesWithAllAuditColumns.length})`
                ),
                React.createElement('div', { className: 'text-sm text-gray-600 dark:text-gray-400' },
                  React.createElement('details', null,
                    React.createElement('summary', { className: 'cursor-pointer hover:text-gray-800 dark:hover:text-gray-200' },
                      'Show compliant tables'
                    ),
                    React.createElement('div', { className: 'mt-2 pl-4 space-y-1' },
                      stats.audit.tablesWithAllAuditColumns.map(tableName =>
                        React.createElement('div', { key: tableName, className: 'text-xs' }, `• ${tableName}`)
                      )
                    )
                  )
                )
              ),

              // Tables with No Audit
              stats.audit.tablesWithNoAuditColumns.length > 0 && React.createElement('div', null,
                React.createElement('h4', { className: 'font-medium text-red-600 dark:text-red-400 mb-2' },
                  `❌ Tables with No Audit Columns (${stats.audit.tablesWithNoAuditColumns.length})`
                ),
                React.createElement('div', { className: 'text-sm text-gray-600 dark:text-gray-400' },
                  React.createElement('details', null,
                    React.createElement('summary', { className: 'cursor-pointer hover:text-gray-800 dark:hover:text-gray-200' },
                      'Show non-compliant tables'
                    ),
                    React.createElement('div', { className: 'mt-2 pl-4 space-y-1' },
                      stats.audit.tablesWithNoAuditColumns.map(tableName =>
                        React.createElement('div', { key: tableName, className: 'text-xs' }, `• ${tableName}`)
                      )
                    )
                  )
                )
              ),

              // Recommendations
              React.createElement('div', { className: 'p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800' },
                React.createElement('h4', { className: 'font-medium text-yellow-800 dark:text-yellow-200 mb-2' }, '💡 Recommendations'),
                React.createElement('ul', { className: 'text-sm text-yellow-700 dark:text-yellow-300 space-y-1' },
                  React.createElement('li', null, '• Add Insert_User and Insert_Date for creation tracking'),
                  React.createElement('li', null, '• Add Update_User and Update_Date for modification tracking'),
                  React.createElement('li', null, '• Add Active/Status column for soft delete functionality'),
                  React.createElement('li', null, '• Consider adding Version column for optimistic locking'),
                  React.createElement('li', null, '• Implement consistent naming conventions across tables')
                )
              )
            )
          )
        )
      )
    )
  )
}

// DiagramView Component
function DiagramView({ schemaData }) {
  const mermaidRef = useRef(null)
  const [diagramCode, setDiagramCode] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)
  const [mermaidReady, setMermaidReady] = useState(false)
  const [diagramContent, setDiagramContent] = useState('')
  const [diagramType, setDiagramType] = useState('none') // 'none', 'loading', 'success', 'error', 'simple'

  useEffect(() => {
    // Check if Mermaid is already loaded
    if (window.mermaid) {
      setMermaidReady(true)
      // Don't auto-generate, let user click the button
    } else {
      // Load Mermaid if not already loaded
      loadMermaid()
    }
  }, [schemaData])

  // Separate effect to handle diagram generation when both schema and mermaid are ready
  useEffect(() => {
    if (schemaData && mermaidReady && mermaidRef.current) {
      // Auto-generate diagram when everything is ready
      setTimeout(() => generateDiagram(), 500)
    }
  }, [schemaData, mermaidReady])

  const loadMermaid = () => {
    if (document.querySelector('script[src*="mermaid"]')) {
      // Script already exists, wait for it to load
      const checkMermaid = setInterval(() => {
        if (window.mermaid) {
          clearInterval(checkMermaid)
          initializeMermaid()
        }
      }, 100)
      return
    }

    const script = document.createElement('script')
    script.src = 'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js'
    script.onload = () => {
      initializeMermaid()
    }
    script.onerror = () => {
      setError('Failed to load Mermaid library. Please check your internet connection.')
    }
    document.head.appendChild(script)
  }

  const initializeMermaid = () => {
    if (window.mermaid) {
      window.mermaid.initialize({
        startOnLoad: false,
        theme: 'default',
        securityLevel: 'loose',
        er: {
          diagramPadding: 20,
          layoutDirection: 'TB',
          minEntityWidth: 100,
          minEntityHeight: 75,
          entityPadding: 15,
          stroke: '#333333',
          fill: '#ECECFF',
          fontSize: 12
        }
      })
      setMermaidReady(true)
      // Don't auto-generate here, let the useEffect handle it
    }
  }

  const generateDiagram = async () => {
    console.log('=== Generate Diagram Called ===')
    console.log('schemaData:', !!schemaData)
    console.log('schemaData.tables:', schemaData?.tables?.length)
    console.log('window.mermaid:', !!window.mermaid)
    console.log('mermaidReady:', mermaidReady)

    // Basic checks
    if (!schemaData || !schemaData.tables) {
      setError('No schema data available')
      setDiagramType('error')
      return
    }

    setIsLoading(true)
    setError(null)
    setDiagramType('loading')

    try {
      // Generate the Mermaid code
      const mermaidCode = generateMermaidDiagram(schemaData)
      setDiagramCode(mermaidCode)
      console.log('Generated Mermaid code length:', mermaidCode.length)

      // If we have Mermaid available, try to render the actual diagram
      if (window.mermaid && mermaidReady) {
        try {
          console.log('Attempting Mermaid render...')
          const diagramId = 'diagram-' + Date.now()

          // Create a temporary container for Mermaid to render into
          const tempDiv = document.createElement('div')
          const { svg } = await window.mermaid.render(diagramId, mermaidCode)

          console.log('Mermaid render successful!')
          setDiagramContent(svg)
          setDiagramType('success')

        } catch (mermaidError) {
          console.error('Mermaid render failed:', mermaidError)
          setDiagramContent(`
            <div class="text-center p-8 bg-yellow-50 rounded-lg">
              <div class="text-4xl mb-4">⚠️</div>
              <h3 class="text-lg font-semibold mb-4">Diagram Rendering Issue</h3>
              <p class="text-gray-700 mb-4">The diagram code was generated but couldn't be rendered visually.</p>
              <p class="text-sm text-gray-600">Try the "Simple View" button instead.</p>
            </div>
          `)
          setDiagramType('error')
        }
      } else {
        // No Mermaid available, show basic info
        setDiagramContent(`
          <div class="text-center p-8">
            <div class="text-2xl mb-4">📊</div>
            <h3 class="text-lg font-semibold mb-4">Schema Information</h3>
            <p class="text-gray-600 mb-4">Found ${schemaData.tables.length} tables with ${schemaData.relationships.length} relationships</p>
            <div class="text-left bg-gray-100 p-4 rounded text-sm max-w-2xl mx-auto">
              <strong>Tables:</strong><br>
              ${schemaData.tables.map(t => `• ${t.name} (${t.columns.length} columns)`).join('<br>')}
            </div>
          </div>
        `)
        setDiagramType('success')
      }

    } catch (err) {
      console.error('Error in generateDiagram:', err)
      setError(`Failed to generate diagram: ${err.message}`)
      setDiagramType('error')
    } finally {
      setIsLoading(false)
    }
  }

  const generateSimpleView = () => {
    if (!schemaData || !schemaData.tables) return

    const simpleContent = `
      <div class="text-center p-8 bg-white rounded-lg shadow-sm border">
        <div class="text-4xl mb-4">📊</div>
        <h3 class="text-lg font-semibold mb-4">Schema Overview</h3>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 max-w-4xl mx-auto">
          ${schemaData.tables.map(table => `
            <div class="bg-blue-50 p-3 rounded border">
              <div class="font-medium text-sm">${table.name}</div>
              <div class="text-xs text-gray-600">${table.columns.length} columns</div>
              ${table.primaryKeys.length > 0 ? `<div class="text-xs text-yellow-600">PK: ${table.primaryKeys.join(', ')}</div>` : ''}
            </div>
          `).join('')}
        </div>
        <p class="text-sm text-gray-600 mt-4">${schemaData.tables.length} tables, ${schemaData.relationships.length} relationships</p>
      </div>
    `

    setDiagramContent(simpleContent)
    setDiagramType('simple')
  }

  const copyDiagramCode = () => {
    navigator.clipboard.writeText(diagramCode)
  }

  if (!schemaData) {
    return React.createElement('div', { className: 'flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900' },
      React.createElement('div', { className: 'text-center' },
        React.createElement('div', { className: 'text-6xl mb-4' }, '📊'),
        React.createElement('h3', { className: 'text-xl font-semibold text-gray-900 dark:text-white mb-2' },
          'No Schema Loaded'
        ),
        React.createElement('p', { className: 'text-gray-600 dark:text-gray-400' },
          'Upload an XML schema file to view the ER diagram'
        )
      )
    )
  }

  return React.createElement('div', { className: 'flex-1 flex flex-col bg-white dark:bg-gray-800' },
    // Toolbar
    React.createElement('div', { className: 'flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700' },
      React.createElement('div', { className: 'flex items-center space-x-4' },
        React.createElement('h2', { className: 'text-lg font-semibold text-gray-900 dark:text-white' },
          'Entity Relationship Diagram'
        ),
        React.createElement('span', { className: 'text-sm text-gray-600 dark:text-gray-400' },
          `${schemaData.tables.length} tables, ${schemaData.relationships.length} relationships`
        )
      ),
      React.createElement('div', { className: 'flex items-center space-x-2' },
        React.createElement('button', {
          onClick: copyDiagramCode,
          className: 'px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors',
          disabled: !diagramCode
        }, '📋 Copy Code'),
        React.createElement('button', {
          onClick: generateDiagram,
          className: 'px-3 py-2 text-sm bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors',
          disabled: isLoading
        }, isLoading ? '⏳ Generating...' : '🔄 Generate Diagram'),
        React.createElement('button', {
          onClick: generateSimpleView,
          className: 'px-3 py-2 text-sm bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 rounded-md hover:bg-green-200 dark:hover:bg-green-800 transition-colors'
        }, '📋 Simple View')
      )
    ),

    // Diagram Content
    React.createElement('div', { className: 'flex-1 overflow-auto bg-gray-50 dark:bg-gray-900' },
      isLoading ?
        React.createElement('div', { className: 'flex items-center justify-center h-full' },
          React.createElement('div', { className: 'text-center' },
            React.createElement('p', { className: 'text-gray-600 dark:text-gray-400' }, 'Generating diagram...')
          )
        ) :
        error ?
          React.createElement('div', { className: 'flex items-center justify-center h-full' },
            React.createElement('div', { className: 'text-center' },
              React.createElement('div', { className: 'text-6xl mb-4' }, '⚠️'),
              React.createElement('h3', { className: 'text-xl font-semibold text-gray-900 dark:text-white mb-2' },
                'Diagram Generation Failed'
              ),
              React.createElement('p', { className: 'text-gray-600 dark:text-gray-400 mb-4' }, error),
              React.createElement('button', {
                onClick: generateDiagram,
                className: 'px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors'
              }, 'Try Again')
            )
          ) :
          React.createElement('div', { className: 'p-8 min-h-full' },
            // Diagram container that uses React state for content
            React.createElement('div', {
              className: 'w-full bg-white rounded-lg border shadow-sm',
              style: { minHeight: '400px' }
            },
              React.createElement('div', {
                className: 'h-full flex items-center justify-center p-8'
              },
                // Render content based on diagram type and state
                diagramType === 'none' && !mermaidReady && React.createElement('div', { className: 'text-center' },
                  React.createElement('div', { className: 'text-4xl mb-4' }, '⏳'),
                  React.createElement('p', { className: 'text-gray-600 dark:text-gray-400' }, 'Loading diagram library...')
                ),
                diagramType === 'none' && mermaidReady && React.createElement('div', { className: 'text-center' },
                  React.createElement('div', { className: 'text-4xl mb-4' }, '📊'),
                  React.createElement('p', { className: 'text-gray-600 dark:text-gray-400' }, 'Click "Generate Diagram" to create the ER diagram'),
                  React.createElement('p', { className: 'text-sm text-gray-500 mt-2' }, 'Or try "Simple View" for a basic table overview')
                ),
                diagramType === 'loading' && React.createElement('div', { className: 'text-center' },
                  React.createElement('div', { className: 'text-4xl mb-4' }, '⏳'),
                  React.createElement('p', { className: 'text-gray-600 dark:text-gray-400' }, 'Generating diagram...')
                ),
                (diagramType === 'success' || diagramType === 'simple' || diagramType === 'error') && diagramContent &&
                  React.createElement('div', {
                    className: 'w-full',
                    dangerouslySetInnerHTML: { __html: diagramContent }
                  })
              )
            ),

            // Fallback code display below the diagram container
            diagramCode && React.createElement('div', { className: 'mt-8 max-w-4xl mx-auto' },
              React.createElement('details', { className: 'bg-gray-50 dark:bg-gray-800 p-4 rounded-lg' },
                React.createElement('summary', { className: 'cursor-pointer font-medium text-gray-900 dark:text-white mb-2' },
                  '📋 View Mermaid Code'
                ),
                React.createElement('pre', {
                  className: 'bg-gray-100 dark:bg-gray-700 p-4 rounded text-sm overflow-x-auto text-gray-800 dark:text-gray-200 mt-2'
                }, diagramCode),
                React.createElement('p', { className: 'text-xs text-gray-600 dark:text-gray-400 mt-2' },
                  'Copy this code to visualize in any Mermaid-compatible tool like GitHub, GitLab, or mermaid.live'
                )
              )
            )
          )
    )
  )
}

// Main App Component
function App() {
  const [schemaData, setSchemaData] = useState(null)
  const [selectedTable, setSelectedTable] = useState(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [currentView, setCurrentView] = useState('explorer')
  const [filters, setFilters] = useState({
    dataTypes: [],
    constraints: [],
    hasRelationships: 'all',
    tableType: 'all'
  })

  // Initialize Mermaid when component mounts
  useEffect(() => {
    if (!window.mermaid) {
      const script = document.createElement('script')
      script.src = 'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js'
      script.onload = () => {
        window.mermaid.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose'
        })
      }
      document.head.appendChild(script)
    }
  }, [])

  return React.createElement('div', { className: 'h-screen flex flex-col bg-gray-50 dark:bg-gray-900' },
    // Top Bar
    React.createElement('header', { className: 'bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700' },
      React.createElement('div', { className: 'flex items-center justify-between px-4 py-3' },
        React.createElement('div', { className: 'flex items-center space-x-4' },
          React.createElement('h1', { className: 'text-xl font-bold text-gray-900 dark:text-white' }, 'SchemaLens'),
          React.createElement(FileUpload, { onSchemaLoad: setSchemaData })
        ),

        React.createElement('div', { className: 'flex items-center space-x-4' },
          React.createElement(SearchBar, {
            searchQuery,
            onSearchChange: setSearchQuery,
            schemaData,
            filters,
            onFiltersChange: setFilters
          }),

          React.createElement('div', { className: 'flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1' },
            React.createElement('button', {
              onClick: () => setCurrentView('explorer'),
              className: `px-3 py-1 rounded text-sm font-medium transition-colors ${
                currentView === 'explorer'
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
              }`
            }, 'Explorer'),
            React.createElement('button', {
              onClick: () => setCurrentView('statistics'),
              className: `px-3 py-1 rounded text-sm font-medium transition-colors ${
                currentView === 'statistics'
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
              }`
            }, 'Statistics'),
            React.createElement('button', {
              onClick: () => setCurrentView('diagram'),
              className: `px-3 py-1 rounded text-sm font-medium transition-colors ${
                currentView === 'diagram'
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
              }`
            }, 'Diagram')
          )
        )
      )
    ),

    // Main Content
    React.createElement('div', { className: 'flex-1 flex overflow-hidden' },
      schemaData ?
        currentView === 'explorer' ?
          React.createElement(TableExplorer, {
            schemaData,
            selectedTable,
            onTableSelect: setSelectedTable,
            searchQuery,
            filters
          }) :
        currentView === 'statistics' ?
          React.createElement(StatisticsView, { schemaData }) :
          React.createElement(DiagramView, { schemaData }) :
        React.createElement('div', { className: 'flex-1 flex items-center justify-center' },
          React.createElement('div', { className: 'text-center' },
            React.createElement('div', { className: 'text-6xl mb-4' }, '📊'),
            React.createElement('h2', { className: 'text-2xl font-semibold text-gray-900 dark:text-white mb-2' },
              'Welcome to SchemaLens'
            ),
            React.createElement('p', { className: 'text-gray-600 dark:text-gray-400 mb-6 max-w-md' },
              'Upload an XML schema file to start exploring your database structure with interactive diagrams and detailed table views.'
            ),
            React.createElement(FileUpload, { onSchemaLoad: setSchemaData })
          )
        )
    )
  )
}

// Render the app
ReactDOM.render(React.createElement(App), document.getElementById('root'))

        
        // Enhanced standalone features
        window.SchemaLens = {
            loadSampleSchema: function() {
                try {
                    const parsedSchema = parseXMLSchema(EMBEDDED_SAMPLE_SCHEMA);
                    console.log('Sample schema loaded:', parsedSchema);
                    return parsedSchema;
                } catch (error) {
                    console.error('Error loading sample:', error);
                    throw error;
                }
            },
            
            version: '1.0.0',
            buildDate: '2025-07-18T16:24:17.595902',
            
            exportSchema: function(schema, format = 'json') {
                if (format === 'json') {
                    return JSON.stringify(schema, null, 2);
                } else if (format === 'markdown') {
                    let md = '# Database Schema

';
                    schema.tables.forEach(table => {
                        md += `## ${table.name}

`;
                        if (table.description) md += `${table.description}

`;
                        md += '| Column | Type | Constraints |
|--------|------|-------------|
';
                        table.columns.forEach(col => {
                            const constraints = [];
                            if (col.isPrimaryKey) constraints.push('PK');
                            if (col.foreignKey) constraints.push('FK');
                            if (!col.nullable) constraints.push('NOT NULL');
                            if (col.isUnique) constraints.push('UNIQUE');
                            md += `| ${col.name} | ${col.type} | ${constraints.join(', ')} |
`;
                        });
                        md += '
';
                    });
                    return md;
                }
                return 'Unsupported format';
            }
        };
        
        // Auto-initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('SchemaLens Standalone Edition v1.0.0 loaded');
            console.log('Use window.SchemaLens.loadSampleSchema() to load the embedded sample');
        });
        
    </script>
    
    <!-- Footer with credits -->
    <div class="no-print fixed bottom-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 text-xs text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-2">
            <span>📊</span>
            <span><strong>SchemaLens</strong> v1.0</span>
            <span>•</span>
            <span>Standalone Edition</span>
        </div>
    </div>
</body>
</html>
