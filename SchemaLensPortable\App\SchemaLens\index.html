<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SchemaLens - Database Schema Explorer</title>
    <meta name="description" content="Interactive database schema explorer and analyzer with advanced filtering and statistics">
    <meta name="keywords" content="database, schema, explorer, analyzer, SQL, XML, ER diagram">
    <meta name="author" content="SchemaLens">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Theme colors -->
    <meta name="theme-color" content="#3b82f6">
    <meta name="msapplication-TileColor" content="#3b82f6">

    <!-- Icons -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>">
    <link rel="apple-touch-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='180' height='180' viewBox='0 0 180 180'><rect width='180' height='180' fill='%233b82f6' rx='20'/><text x='90' y='120' text-anchor='middle' font-size='100'>📊</text></svg>">

    <!-- iOS specific -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="SchemaLens">

    <!-- Windows specific -->
    <meta name="msapplication-starturl" content="/">
    <meta name="msapplication-config" content="/browserconfig.xml">
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      .search-highlight {
        background-color: #fef3c7;
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
      }

      .dark .search-highlight {
        background-color: #92400e;
        color: #fef3c7;
      }

      .custom-scrollbar::-webkit-scrollbar {
        width: 8px;
      }

      .custom-scrollbar::-webkit-scrollbar-track {
        background: #f1f5f9;
      }

      .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 4px;
      }

      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
      }

      .dark .custom-scrollbar::-webkit-scrollbar-track {
        background: #1e293b;
      }

      .dark .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #475569;
      }

      .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #64748b;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="text/babel" src="./src/app.js"></script>

    <!-- PWA Service Worker Registration -->
    <script>
      // Register service worker for PWA functionality
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then(registration => {
              console.log('SchemaLens SW registered:', registration);

              // Check for updates
              registration.addEventListener('updatefound', () => {
                const newWorker = registration.installing;
                newWorker.addEventListener('statechange', () => {
                  if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                    // New version available
                    if (confirm('New version of SchemaLens is available. Reload to update?')) {
                      window.location.reload();
                    }
                  }
                });
              });
            })
            .catch(error => {
              console.log('SchemaLens SW registration failed:', error);
            });
        });
      }

      // PWA install prompt
      let deferredPrompt;
      window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;

        // Show install button or banner
        const installBanner = document.createElement('div');
        installBanner.innerHTML = `
          <div style="position: fixed; top: 0; left: 0; right: 0; background: #3b82f6; color: white; padding: 12px; text-align: center; z-index: 1000;">
            <span>📱 Install SchemaLens as an app for better experience</span>
            <button onclick="installPWA()" style="margin-left: 12px; background: white; color: #3b82f6; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer;">Install</button>
            <button onclick="this.parentElement.parentElement.remove()" style="margin-left: 8px; background: transparent; color: white; border: 1px solid white; padding: 6px 12px; border-radius: 4px; cursor: pointer;">Later</button>
          </div>
        `;
        document.body.appendChild(installBanner);
      });

      window.installPWA = async () => {
        if (deferredPrompt) {
          deferredPrompt.prompt();
          const { outcome } = await deferredPrompt.userChoice;
          console.log('PWA install outcome:', outcome);
          deferredPrompt = null;

          // Remove install banner
          const banner = document.querySelector('[style*="position: fixed"]');
          if (banner) banner.parentElement.remove();
        }
      };

      // Handle app installed
      window.addEventListener('appinstalled', (evt) => {
        console.log('SchemaLens PWA installed successfully');
      });
    </script>
  </body>
</html>
